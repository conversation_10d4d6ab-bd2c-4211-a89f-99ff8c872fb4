"use client";
export const dynamic = "force-dynamic";

import { useAuthState } from "react-firebase-hooks/auth";
import { useCollection, useDocument } from "react-firebase-hooks/firestore";
import {
    collection,
    query,
    where, // Add this
    doc,
    updateDoc,
    deleteDoc,
    getDoc
} from "firebase/firestore";

import { auth, db } from "../../lib/firebase/firebase";
import { useState, useEffect } from "react";
import { startOfWeek, eachDayOfInterval, addDays, isSameDay, format, differenceInMinutes } from "date-fns";
import { Trash2, Pencil } from "lucide-react";
import { Timestamp } from "firebase/firestore";

export default function AdminPage() {
    const [user, loading, error] = useAuthState(auth);
    const [startDate, setStartDate] = useState("");
    const [endDate, setEndDate] = useState("");
    const [selectedEmployee, setSelectedEmployee] = useState("");
    const [currentUserDoc] = useDocument(user ? doc(db, "users", user?.uid) : null);

    const usersQuery = currentUserDoc?.data()?.branch
        ? query(
            collection(db, "users"),
            where("branch", "==", currentUserDoc.data().branch)
        )
        : null;

    const timeQuery = currentUserDoc?.data()?.branch
        ? query(
            collection(db, "employeeTime"),
            where("branch", "==", currentUserDoc.data().branch)
        )
        : null;

    const [usersSnapshot, usersLoading] = useCollection(usersQuery);
    const [timeSnapshot, timeLoading] = useCollection(timeQuery);


    useEffect(() => {
        console.log("AdminPage mounted");
    }, []);

    if (loading || timeLoading || usersLoading) {
        return <div className="text-center text-gray-500 py-20">Loading...</div>;
    }

    if (error) {
        return <div className="text-center text-red-500 py-20">Error: {error.message}</div>;
    }

    if (!user || !currentUserDoc || !currentUserDoc.data().isAdmin) {
        return <div className="text-center text-gray-500 py-20">Admin access only</div>;
    }

    const users = usersSnapshot?.docs.map(doc => ({ id: doc.id, ...doc.data() })) || [];
    const allEntries = timeSnapshot?.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
    })) || [];

    const filteredEntries = allEntries.filter(entry => {
        // Entries are already pre-filtered by branch in the query
        const entryDate = entry.date.toDate();
        return (
            (!startDate || entryDate >= new Date(startDate)) &&
            (!endDate || entryDate <= new Date(endDate)) &&
            (!selectedEmployee || entry.userId === selectedEmployee)
        );
    });


    const employeeWeeks = {};

    filteredEntries.forEach(entry => {
        const employeeId = entry.userId;
        const weekStart = startOfWeek(entry.date.toDate(), { weekStartsOn: 0 });
        const weekKey = `${employeeId}_${weekStart.getTime()}`;

        if (!employeeWeeks[employeeId]) employeeWeeks[employeeId] = {};
        if (!employeeWeeks[employeeId][weekKey]) employeeWeeks[employeeId][weekKey] = [];
        employeeWeeks[employeeId][weekKey].push(entry);
    });

    const handleEdit = async (entryId, updatedData) => {
        try {
            const entryRef = doc(db, "employeeTime", entryId);
            const entrySnap = await getDoc(entryRef);
            const currentEntry = entrySnap.data();
            const mergedData = { ...currentEntry, ...updatedData };
            await updateDoc(entryRef, {
                ...mergedData,
                hours: calculateHours(mergedData)
            });
        } catch (error) {
            console.error("Error updating entry:", error);
            alert("Failed to update entry");
        }
    };

    const handleDelete = async (entryId) => {
        if (confirm("Are you sure you want to delete this entry?")) {
            try {
                await deleteDoc(doc(db, "employeeTime", entryId));
            } catch (error) {
                console.error("Error deleting entry:", error);
                alert("Failed to delete entry");
            }
        }
    };

    return (
        <div className="w-full max-w-6xl mx-auto p-6 bg-white rounded-xl shadow-lg">
            <h1 className="text-3xl font-bold text-gray-800 mb-6">Admin Dashboard</h1>
            <p className="text-gray-600 mt-2">
                Viewing data for branch: {currentUserDoc.data().branch.toUpperCase()}
            </p>
            <div className="mb-8 bg-gray-50 p-6 rounded-lg shadow-inner">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                        <input
                            type="date"
                            value={startDate}
                            onChange={(e) => setStartDate(e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                        <input
                            type="date"
                            value={endDate}
                            onChange={(e) => setEndDate(e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Employee</label>
                        <select
                            value={selectedEmployee}
                            onChange={(e) => setSelectedEmployee(e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="">All Employees</option>
                            {users.map(u => (
                                <option key={u.id} value={u.id}>
                                    {u.displayName || u.email}
                                </option>
                            ))}
                        </select>
                    </div>
                </div>
            </div>

            <div className="space-y-8">
                {users.map((employee) => {
                    const employeeEntries = employeeWeeks[employee.id] || {};
                    return (
                        <div key={employee.id} className="mb-8">
                            <div className="flex justify-between items-center mb-4">
                                <h2 className="text-2xl font-semibold text-gray-800">
                                    {employee.displayName || employee.email}
                                </h2>
                                <WageEditor employee={employee} />
                            </div>

                            {Object.entries(employeeEntries).length > 0 ? (
                                Object.entries(employeeEntries).map(([weekKey, weekEntries]) => {
                                    const weekStart = new Date(parseInt(weekKey.split('_')[1]));
                                    return (
                                        <WeeklySummary
                                            key={weekKey}
                                            weekStart={weekStart}
                                            entries={weekEntries}
                                            onEdit={handleEdit}
                                            onDelete={handleDelete}
                                            overtimeRate={employee.overtimeRate || 1.5}
                                        />
                                    );
                                })
                            ) : (
                                <div className="bg-gray-50 p-4 rounded-lg text-center text-gray-500">
                                    No time entries recorded yet
                                </div>
                            )}
                        </div>
                    );
                })}
            </div>
        </div>
    );
}

function WageEditor({ employee }) {
    const [isEditing, setIsEditing] = useState(false);
    const [newPaymentType, setNewPaymentType] = useState(employee.paymentType || 'hourly');
    const [newWage, setNewWage] = useState(employee.wage ? employee.wage.toString() : "16");
    const [newCommissionRate, setNewCommissionRate] = useState(
        employee.commissionRate ? employee.commissionRate.toString() : "0.27"
    );
    const [newOvertimeRate, setNewOvertimeRate] = useState(
        employee.overtimeRate ? employee.overtimeRate.toString() : "1.5"
    );

    const handleSave = async () => {
        try {
            await updateDoc(doc(db, "users", employee.id), {
                paymentType: newPaymentType,
                wage: parseFloat(newWage),
                commissionRate: parseFloat(newCommissionRate),
                overtimeRate: parseFloat(newOvertimeRate)
            });
            setIsEditing(false);
        } catch (error) {
            console.error("Error updating payment settings:", error);
            alert("Failed to update");
        }
    };

    return (
        <div className="flex items-center gap-2">
            {isEditing ? (
                <>
                    <div>
                        <label className="block text-sm font-medium text-gray-700">Type</label>
                        <select
                            value={newPaymentType}
                            onChange={(e) => setNewPaymentType(e.target.value)}
                            className="w-24 p-2 border border-gray-300 rounded-md"
                        >
                            <option value="hourly">Hourly</option>
                            <option value="commission">Commission</option>
                        </select>
                    </div>

                    {newPaymentType === 'hourly' ? (
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Wage ($/hr)</label>
                            <input
                                type="number"
                                step="0.01"
                                value={newWage}
                                onChange={(e) => setNewWage(e.target.value)}
                                className="w-24 p-2 border border-gray-300 rounded-md"
                            />
                        </div>
                    ) : (
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Commission Rate</label>
                            <input
                                type="number"
                                step="0.01"
                                value={newCommissionRate}
                                onChange={(e) => setNewCommissionRate(e.target.value)}
                                className="w-24 p-2 border border-gray-300 rounded-md"
                                min="0"
                                max="1"
                            />
                        </div>
                    )}

                    <div>
                        <label className="block text-sm font-medium text-gray-700">OT Rate</label>
                        <input
                            type="number"
                            step="0.1"
                            value={newOvertimeRate}
                            onChange={(e) => setNewOvertimeRate(e.target.value)}
                            className="w-16 p-2 border border-gray-300 rounded-md"
                        />
                    </div>

                    <button
                        onClick={handleSave}
                        className="px-2 py-1 bg-green-600 text-white rounded-md hover:bg-green-700"
                    >
                        Save
                    </button>
                    <button
                        onClick={() => setIsEditing(false)}
                        className="px-2 py-1 bg-gray-300 rounded-md hover:bg-gray-400"
                    >
                        Cancel
                    </button>
                </>
            ) : (
                <>
                    <span className="text-gray-700">
                        {employee.paymentType === 'commission' ?
                            `Commission: ${(employee.commissionRate * 100).toFixed(0)}%` :
                            `Wage: $${employee.wage?.toFixed(2) || '0.00'}/hr`}
                    </span>
                    <span className="text-gray-700">
                        OT Rate: {employee.overtimeRate?.toFixed(1) || '1.5'}x
                    </span>
                    <button
                        onClick={() => setIsEditing(true)}
                        className="text-blue-600 hover:text-blue-800"
                    >
                        <Pencil className="h-4 w-4" />
                    </button>
                </>
            )}
        </div>
    );
}

// Removed TS interface WeeklySummaryProps; using plain props instead
function WeeklySummary({ weekStart, entries, onEdit, onDelete, overtimeRate }) {
    const [editingEntryId, setEditingEntryId] = useState(null);

    const handleTimeUpdate = (entryId, field, value) => {
        const updatedData = { [field]: value };
        onEdit(entryId, updatedData);
    };

    const startEditing = (entryId) => {
        setEditingEntryId(entryId);
    };

    const stopEditing = () => {
        setEditingEntryId(null);
    };

    const days = eachDayOfInterval({ start: weekStart, end: addDays(weekStart, 6) });
    const dailyTotals = days.map(day => {
        const dayEntries = entries.filter(entry => isSameDay(entry.date.toDate(), day));
        const totalHours = dayEntries.reduce((sum, entry) => sum + parseFloat(entry.hours || "0"), 0);
        const dailyWage = dayEntries.reduce(
            (sum, entry) => sum + parseFloat(entry.hours || "0") * entry.wage,
            0
        );
        return { day, totalHours, dailyWage, entries: dayEntries };
    });

    const weeklyTotalHours = dailyTotals.reduce((sum, { totalHours }) => sum + totalHours, 0);
    const weeklyTotalWage = dailyTotals.reduce((sum, { dailyWage }) => sum + dailyWage, 0);
    const overtimeHours = Math.max(weeklyTotalHours - 40, 0);
    const overtimeWage = overtimeHours * (entries[0]?.wage || 0) * overtimeRate;

    return (
        <div className="mb-8 bg-gray-50 p-4 rounded-lg shadow-md">
            <h3 className="text-lg font-medium text-gray-800 mb-2">
                Week of {format(weekStart, "MMMM d, yyyy")}
            </h3>
            <table className="w-full border-collapse">
                <thead>
                    <tr className="bg-gray-200">
                        <th className="border p-2 text-left">Day</th>
                        <th className="border p-2 text-left">Start</th>
                        <th className="border p-2 text-left">End</th>
                        <th className="border p-2 text-left">Wage</th>
                        <th className="border p-2 text-left">Break</th>
                        <th className="border p-2 text-right">Hours</th>
                        <th className="border p-2 text-right">Daily Wage</th>
                        <th className="border p-2 text-right">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {dailyTotals.map(({ day, totalHours, dailyWage, entries }) =>
                        entries.map(entry => (
                            <tr key={entry.id} className="hover:bg-gray-100">
                                <td className="border p-2">{format(day, "EEE M/d")}</td>
                                <td className="border p-2">
                                    {editingEntryId === entry.id ? (
                                        <input
                                            type="time"
                                            value={entry.startTime}
                                            onChange={(e) => handleTimeUpdate(entry.id, "startTime", e.target.value)}
                                            className="w-full p-1 border rounded"
                                            onBlur={stopEditing}
                                        />
                                    ) : (
                                        <span>{entry.startTime}</span>
                                    )}
                                </td>
                                <td className="border p-2">
                                    {editingEntryId === entry.id ? (
                                        <input
                                            type="time"
                                            value={entry.endTime}
                                            onChange={(e) => handleTimeUpdate(entry.id, "endTime", e.target.value)}
                                            className="w-full p-1 border rounded"
                                            onBlur={stopEditing}
                                        />
                                    ) : (
                                        <span>{entry.endTime}</span>
                                    )}
                                </td>
                                <td className="border p-2">${(entry.wage ?? 0).toFixed(2)}</td>
                                <td className="border p-2">
                                    {editingEntryId === entry.id ? (
                                        <div className="flex items-center gap-2">
                                            <input
                                                type="checkbox"
                                                checked={entry.tookBreak}
                                                onChange={(e) => handleTimeUpdate(entry.id, "tookBreak", e.target.checked)}
                                                className="h-4 w-4"
                                            />
                                            <input
                                                type="number"
                                                value={entry.breakMinutes || 0}
                                                onChange={(e) =>
                                                    handleTimeUpdate(entry.id, "breakMinutes", parseInt(e.target.value) || 0)
                                                }
                                                className="w-16 p-1 border rounded"
                                                disabled={!entry.tookBreak}
                                                onBlur={stopEditing}
                                            />
                                        </div>
                                    ) : (
                                        <span>{entry.tookBreak ? `${entry.breakMinutes} min` : "No break"}</span>
                                    )}
                                </td>
                                <td className="border p-2 text-right">{entry.hours}</td>
                                <td className="border p-2 text-right">
                                    {entry.paymentType === 'commission' ?
                                        `$${(entry.jobRevenue * entry.commissionRate).toFixed(2)}` :
                                        `$${(entry.hours * entry.wage).toFixed(2)}`
                                    }
                                </td>
                                <td className="border p-2 text-right">
                                    {entry.paymentType === 'commission' ?
                                        `Commission (${(entry.commissionRate * 100).toFixed(0)}%)` :
                                        `${entry.hours} hrs`
                                    }
                                </td>

                                <td className="border p-2 text-right">
                                    {editingEntryId === entry.id ? (
                                        <button
                                            onClick={stopEditing}
                                            className="text-green-600 hover:text-green-800 mr-2"
                                        >
                                            Save
                                        </button>
                                    ) : (
                                        <button
                                            onClick={() => startEditing(entry.id)}
                                            className="text-blue-600 hover:text-blue-800 mr-2"
                                        >
                                            <Pencil className="h-4 w-4" />
                                        </button>
                                    )}
                                    <button
                                        onClick={() => onDelete(entry.id)}
                                        className="text-red-600 hover:text-red-800"
                                    >
                                        <Trash2 className="h-4 w-4" />
                                    </button>
                                </td>
                            </tr>
                        ))
                    )}
                    <tr className="bg-gray-100 font-semibold">
                        <td className="border p-2" colSpan="5">
                            Total Hours
                        </td>
                        <td className="border p-2 text-right">{weeklyTotalHours.toFixed(1)}</td>
                        <td className="border p-2 text-right">${weeklyTotalWage.toFixed(2)}</td>
                        <td className="border p-2"></td>
                    </tr>
                    <tr className="bg-gray-100 font-semibold">
                        <td className="border p-2" colSpan="5">
                            Overtime (@ {overtimeRate.toFixed(1)}x)
                        </td>
                        <td className="border p-2 text-right">{overtimeHours.toFixed(1)}</td>
                        <td className="border p-2 text-right">${overtimeWage.toFixed(2)}</td>
                        <td className="border p-2"></td>
                    </tr>
                </tbody>
            </table>
        </div>
    );
}

function calculateHours(entry) {
    const entryDate = entry.date?.toDate();
    if (!entryDate || !entry.startTime || !entry.endTime) {
        return "0.0";
    }
    const start = new Date(`${format(entryDate, "yyyy-MM-dd")}T${entry.startTime}`);
    const end = new Date(`${format(entryDate, "yyyy-MM-dd")}T${entry.endTime}`);
    const totalMinutes = differenceInMinutes(end, start);
    const breakTime = entry.tookBreak ? (entry.breakMinutes || 0) : 0;
    return Math.max((totalMinutes - breakTime) / 60, 0).toFixed(1);
}
