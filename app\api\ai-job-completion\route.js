import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY
});

export async function POST(req) {
    try {
        console.log('AI job completion API called');
        const { eventData } = await req.json();
        console.log('Received event data:', eventData);

        if (!eventData) {
            console.log('No event data provided');
            return new Response(
                JSON.stringify({ error: 'Event data is required' }),
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // Extract event information
        const {
            title,
            description,
            location,
            start,
            end
        } = eventData;

        // Calculate duration
        const startTime = new Date(start);
        const endTime = new Date(end);
        const durationHours = (endTime - startTime) / (1000 * 60 * 60);

        // Create a comprehensive prompt for the AI
        const systemMessage = {
            role: 'system',
            content: `You are an AI assistant that helps auto-fill job completion forms for a mobile car detailing service called "Detail On The Go". 

Your task is to analyze calendar event information and extract/infer relevant details to populate a job completion form.

The job completion form has these fields:
- vehicle: Vehicle information (year, make, model, type)
- customerName: Customer's name
- customerPhone: Customer's phone number
- customerEmail: Customer's email address
- customerAddress: Service location/address
- package: Service package (Interior, Exterior, Combo, etc.)
- paymentCollected: Payment method (Card, Cash, Check, etc.)
- notes: Additional notes about the job

IMPORTANT INSTRUCTIONS:
1. Extract information directly from the event data when available
2. Make reasonable inferences based on typical car detailing patterns
3. If information is not available or cannot be reasonably inferred, leave the field empty
4. For vehicle information, try to extract year, make, model from the title or description
5. Customer name is often in the event title
6. Look for phone numbers, emails, and addresses in the description
7. Infer service package based on event duration and any keywords in title/description
8. Return ONLY a valid JSON object with the form fields
9. Do not include explanations or additional text

Example response format:
{
  "vehicle": "2020 Honda CR-V",
  "customerName": "John Smith",
  "customerPhone": "+1234567890",
  "customerEmail": "<EMAIL>",
  "customerAddress": "123 Main St, City, State",
  "package": "Interior + Exterior Combo",
  "paymentCollected": "Card",
  "notes": "Customer requested extra attention to pet hair removal"
}`
        };

        const userMessage = {
            role: 'user',
            content: `Please analyze this calendar event and extract job completion information:

Event Title: ${title || 'No title'}
Event Description: ${description || 'No description'}
Event Location: ${location || 'No location'}
Event Start: ${startTime.toLocaleString()}
Event End: ${endTime.toLocaleString()}
Duration: ${durationHours.toFixed(1)} hours

Extract and return the job completion form data as JSON.`
        };

        console.log('Calling OpenAI with messages:', [systemMessage, userMessage]);

        const completion = await openai.chat.completions.create({
            model: 'gpt-4-turbo',
            messages: [systemMessage, userMessage],
            max_tokens: 500,
            temperature: 0.1, // Low temperature for more consistent extraction
            response_format: { type: "json_object" }
        });

        const aiResponse = completion.choices[0].message.content.trim();
        console.log('OpenAI response:', aiResponse);
        
        try {
            const jobData = JSON.parse(aiResponse);
            
            // Validate the response has the expected structure
            const expectedFields = ['vehicle', 'customerName', 'customerPhone', 'customerEmail', 'customerAddress', 'package', 'paymentCollected', 'notes'];
            const responseFields = Object.keys(jobData);
            
            // Ensure all expected fields are present (even if empty)
            const completeJobData = {};
            expectedFields.forEach(field => {
                completeJobData[field] = jobData[field] || '';
            });

            return new Response(
                JSON.stringify({ 
                    success: true, 
                    jobData: completeJobData,
                    eventInfo: {
                        title,
                        duration: `${durationHours.toFixed(1)} hours`,
                        location
                    }
                }),
                { status: 200, headers: { 'Content-Type': 'application/json' } }
            );

        } catch (parseError) {
            console.error('Failed to parse AI response as JSON:', parseError);
            return new Response(
                JSON.stringify({ error: 'Failed to parse AI response' }),
                { status: 500, headers: { 'Content-Type': 'application/json' } }
            );
        }

    } catch (error) {
        console.error('Error in AI job completion:', error);
        return new Response(
            JSON.stringify({ error: error.message }),
            { status: 500, headers: { 'Content-Type': 'application/json' } }
        );
    }
}

export async function GET(req) {
    return new Response(
        JSON.stringify({ error: 'This endpoint only accepts POST requests' }),
        { status: 405, headers: { 'Content-Type': 'application/json' } }
    );
}
