import { NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(request) {
  try {
    const { text } = await request.json();

    if (!text || !text.trim()) {
      return NextResponse.json({ error: 'No text provided' }, { status: 400 });
    }

    const prompt = `
You are an AI assistant that extracts invoice information from text and returns structured JSON data.

Parse the following text and extract invoice information. Return a JSON object with this exact structure:

{
  "billTo": {
    "company": "string",
    "address": "string", 
    "city": "string",
    "state": "string",
    "zip": "string",
    "country": "string"
  },
  "invoiceDetails": {
    "serviceDate": "YYYY-MM-DD",
    "invoiceDate": "YYYY-MM-DD", 
    "paymentTerms": "string"
  },
  "services": [
    {
      "description": "string",
      "unitNumber": "string",
      "quantity": number,
      "rate": number
    }
  ]
}

Rules:
- Extract company name, address components (street, city, state, zip, country)
- Parse dates and convert to YYYY-MM-DD format
- Extract service descriptions, vehicle/unit numbers, quantities, and rates
- If multiple vehicles are mentioned for the same service, create separate service entries
- Use reasonable defaults: quantity=1 if not specified, country="USA" if not specified
- If payment terms not mentioned, use "Due Immediately"
- If invoice date not mentioned, use today's date
- Only include fields that can be extracted from the text
- Return valid JSON only, no additional text

Text to parse:
${text}
`;

    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are a helpful assistant that extracts structured invoice data from text. Always return valid JSON only."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.1,
      max_tokens: 1500,
    });

    const responseText = completion.choices[0].message.content.trim();
    
    // Try to parse the JSON response
    let parsedData;
    try {
      parsedData = JSON.parse(responseText);
    } catch (parseError) {
      console.error('Failed to parse AI response as JSON:', responseText);
      return NextResponse.json({ error: 'Failed to parse invoice data' }, { status: 500 });
    }

    // Validate the structure
    if (!parsedData || typeof parsedData !== 'object') {
      return NextResponse.json({ error: 'Invalid response format' }, { status: 500 });
    }

    // Ensure services is an array
    if (parsedData.services && !Array.isArray(parsedData.services)) {
      parsedData.services = [];
    }

    // Clean up any undefined or null values
    const cleanData = {
      billTo: parsedData.billTo || {},
      invoiceDetails: parsedData.invoiceDetails || {},
      services: parsedData.services || []
    };

    return NextResponse.json(cleanData);

  } catch (error) {
    console.error('Auto-fill API error:', error);
    return NextResponse.json(
      { error: 'Failed to process auto-fill request' },
      { status: 500 }
    );
  }
}
