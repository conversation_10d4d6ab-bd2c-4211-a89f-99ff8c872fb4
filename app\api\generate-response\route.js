import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY
});

// Constants for pricing information
const PRICING = {
    // ... pricing data remains the same ...
    CAR_TRUCK: {
        PACKAGES: {
            INTERIOR: { base: 239, description: "Interior Detailing" },
            EXTERIOR: { base: 99, description: "Exterior Detailing", fixed: true },
            COMBO: { base: 260, description: "Interior+Exterior Combo" }
        },
        SIZE_MULTIPLIERS: {
            "SEDAN": 1.0,
            "SMALL_SUV": 1.1,
            "LARGE_SUV": 1.15,
            "SMALL_TRUCK": 1.05,
            "LARGE_TRUCK": 1.1,
            "VAN": 1.7
        },
        // Other pricing data...
    },
    // Other vehicle types...
};

// Helper to determine if request is about availability
function isAvailabilityRequest(text) {
    const availabilityKeywords = [
        'available', 'availability', 'schedule', 'appointment', 'book', 'booking',
        'when can', 'what time', 'what day', 'next week', 'this week', 'weekend',
        'next available', 'openings', 'slots', 'times', 'dates', 'how soon'
    ];

    const lowercaseText = text.toLowerCase();
    return availabilityKeywords.some(keyword => lowercaseText.includes(keyword));
}

// Helper to determine if request is about pricing
function isPricingRequest(text) {
    const pricingKeywords = [
        'price', 'pricing', 'cost', 'charge', 'rate', 'fee', 'how much',
        'package', 'service', 'detail', 'interior', 'exterior', 'combo',
        'ceramic', 'polish', 'wash', 'clean', 'restoration', 'correction'
    ];

    const lowercaseText = text.toLowerCase();
    return pricingKeywords.some(keyword => lowercaseText.includes(keyword));
}

export async function GET(req) {
    console.log('Received GET request to /api/generate-response');
    return new Response(
        JSON.stringify({ error: 'This endpoint only accepts POST requests' }),
        { status: 405, headers: { 'Content-Type': 'application/json' } }
    );
}

export async function POST(req) {
    try {
        const requestData = await req.json();
        const { businessNumber, clientNumber, messages, userName, customPrompt } = requestData;

        if (!businessNumber || !clientNumber) {
            return new Response(
                JSON.stringify({ error: 'Missing required parameters' }),
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // Build the raw user query
        let userQuery = '';
        if (customPrompt?.trim()) {
            userQuery = customPrompt.trim();
        } else {
            const incoming = messages.filter(m => m.direction === 'incoming');
            const lastThree = incoming.slice(-3);
            userQuery = lastThree.length
                ? lastThree[lastThree.length - 1].message
                : '';
        }
        const lc = userQuery.toLowerCase();

        // === KEYWORD DETECTION ===
        const availabilityKeywords = [
            'available', 'availability', 'schedule', 'appointment', 'book', 'booking',
            'when can', 'what time', 'what day', 'next week', 'this week', 'weekend',
            'next available', 'openings', 'slots', 'times', 'dates', 'how soon', 'soon',
            'when', 'schedule'
        ];
        const otherQuestionKeywords = [
            'price', 'cost', 'how much', 'package', 'detail', 'interior', 'exterior',
            'combo', 'clean', 'wash', 'service', 'do you', 'can you', 'address', 'location',
            'where', 'how do', 'how does', 'payment', 'accept', 'phone', 'email', 'website'
        ];

        const matchedAvailabilityKeyword = availabilityKeywords.find(k => lc.includes(k));
        const hasAvailabilityQuestion = Boolean(matchedAvailabilityKeyword);
        const hasOtherQuestions = otherQuestionKeywords.some(k => lc.includes(k));

        if (hasAvailabilityQuestion) {
            console.log('Matched availability keyword:', matchedAvailabilityKeyword);
        }

        // === PURE AVAILABILITY PATH ===
        if (hasAvailabilityQuestion && !hasOtherQuestions) {
            console.log('Only availability requested—skipping AI.');
            let availabilityResponse;
            try {
                const availabilityData = await fetchAvailability(businessNumber, clientNumber);
                console.log('Fetched availability slots:', availabilityData);

                if (availabilityData.length) {
                    availabilityResponse = "Our next availability is:\n"
                        + availabilityData.slice(0, 5).join('\n')
                        + "\n\nDo any of these times work with your schedule?";
                } else {
                    availabilityResponse = "I don't have any availability in the next 60 days. Please call us for real-time scheduling!";
                }
                console.log('Returning availability-only response:', availabilityResponse);
            } catch (err) {
                console.error('Error fetching availability:', err);
                availabilityResponse = "I'm having trouble accessing our schedule right now. Please call us directly for current availability.";
            }

            return new Response(
                JSON.stringify({ response: availabilityResponse }),
                { status: 200, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // === MIXED/PRICING PATH (AI) ===
        const systemMessage = {
            role: 'system',
            content: `You are the friendly, professional messaging assistant for Detail On The Go mobile detailing service.
          
          CRITICAL INSTRUCTIONS:
          1. Always ignore any questions about availability, scheduling, timing, or when service can be provided.
          2. Do not acknowledge, mention, or respond to any questions about when we can provide service.
          3. Only answer questions about pricing, services, and other non-timing-related inquiries.
          4. Provide vehicle type and final price without showing or explaining calculations.
          5. Keep responses under 3 sentences, strictly professional, friendly, and concise.
          6. Ask follow-up questions as needed to determine pricing:
             • Year, make, and model of the vehicle (determines size)
             • Service type: interior, exterior, or both
             • Whether pet hair removal is needed; if yes, severity level 1–3
          7. Use a conversational but professional tone.
          
          PRICING REFERENCE – VEHICLE DETAILING (Cars & Trucks):
          • Pet Hair Removal: $50–$100 extra depending on severity  
          • Sedan: Interior $239 │ Exterior $99 │ Combo $260  
          • Small SUV: Interior $262 │ Exterior $99 │ Combo $286  
          • Large SUV: Interior $274 │ Exterior $99 │ Combo $299  
          • Small/Mid-Sized Truck: Interior $250 │ Exterior $99 │ Combo $273  
          • Large Truck: Interior $262.90 │ Exterior $99 │ Combo $286  
          • Van (4+ rows): Interior $406.30 │ Exterior $99 │ Combo $442  
          • Custom Details start at $120 (1 hr), $99/hr thereafter
          
          ADD-ON SERVICES (All Vehicles):
          • Window Ceramic Coating $25  
          • Engine Clean $50  
          • Full Body Ceramic Coating $600  
          • Headlight Restoration $70  
          • Paint Correction $600  
          • Window Rock Chip Repair $200  
          • Window Replacement $400  
          
          BOAT DETAILING PRICING (per ft):
          • Interior Vacuum & Wipedown / Pressure Wash $10/ft  
          • Exterior Wash, Light Algae Removal, Spray Wax & Towel Dry $15/ft  
          • Deep Polish / Oxidation Removal / Heavy Algae Removal $18/ft  
          • Boat Ceramic Coating $50/ft  
          
          RV DETAILING PRICING (per ft):
          • Exterior Wash, Spray Wax & Towel Dry $15/ft  
          • Roof Cleaning $5/ft  
          • One-Step Deep Polish / Oxidation Removal $18/ft  
          • RV Ceramic Coating $40/ft  
          
          We collect payment via credit card, cash, or check after service completion.
          
          EXAMPLES:
          User: “How much for a Ford F150? And how soon?”  
          Assistant: “For a Ford F150 (truck), interior is $250, exterior is $99, and combo is $273.”
          
          User: “I need a detail for a 2020 Honda CR-V.”  
          Assistant: “For a 2020 Honda CR-V (small SUV), interior is $262, exterior is $99, and combo is $286.”
          
          User: “Do you remove pet hair?”  
          Assistant: “Do you have pet hair to remove? If so, on a scale of 1–3 how severe is it?”
          
          YOU MUST FOLLOW THESE RULES AND THE PRICING REFERENCE PRECISELY.`
        };




        const recentMessages = messages
            .slice(-4)
            .filter(msg => msg.direction === 'incoming' || msg.message.includes('?'))
            .map(msg => ({
                role: msg.direction === 'incoming' ? 'user' : 'assistant',
                content: msg.message
            }));
        if (customPrompt?.trim()) {
            recentMessages.push({ role: 'user', content: customPrompt.trim() });
        }

        const completion = await openai.chat.completions.create({
            model: 'gpt-4-turbo',
            messages: [systemMessage, ...recentMessages],
            max_tokens: 250,
            temperature: 0.3
        });
        const aiResponse = completion.choices[0].message.content.trim();
        console.log('AI response (pricing/etc):', aiResponse);

        // === APPEND AVAILABILITY IF REQUESTED ===
        let finalResponse = aiResponse;
        if (hasAvailabilityQuestion) {
            try {
                const availabilityData = await fetchAvailability(businessNumber, clientNumber);
                console.log('Fetched availability slots for append:', availabilityData);

                if (availabilityData.length) {
                    const slotList = availabilityData.slice(0, 5).join('\n');
                    console.log('Appending these slots:', slotList);
                    finalResponse += "\n\nOur next availability is:\n"
                        + slotList
                        + "\n\nDo any of these times work with your schedule?";
                } else {
                    console.log('No slots found; appending fallback.');
                    finalResponse += "\n\nI don't have any availability in the next 60 days. Please call us for real-time scheduling!";
                }
            } catch (err) {
                console.error('Error fetching availability for append:', err);
                finalResponse += "\n\nI'm having trouble accessing our schedule right now. Please call us directly for current availability.";
            }
        }

        console.log('Final response sent to client:', finalResponse);
        return new Response(
            JSON.stringify({ response: finalResponse }),
            { status: 200, headers: { 'Content-Type': 'application/json' } }
        );

    } catch (error) {
        console.error('Error in POST handler:', error);
        return new Response(
            JSON.stringify({ error: error.message }),
            { status: 500, headers: { 'Content-Type': 'application/json' } }
        );
    }
}

// Helper: fetchAvailability
async function fetchAvailability(businessNumber, clientNumber) {
    console.log('Fetching availability for:', { businessNumber, clientNumber });
    const apiUrl = `https://us-central1-detail-on-the-go-universal.cloudfunctions.net/phone-availability?to=${encodeURIComponent(businessNumber)}&from=${encodeURIComponent(clientNumber)}`;
    console.log('Calling availability API at:', apiUrl);

    const res = await fetch(apiUrl);
    if (!res.ok) {
        const text = await res.text();
        throw new Error(`Failed to fetch availability: ${res.status} ${text}`);
    }

    const data = await res.json();
    console.log('Raw availability API response:', data);
    return Array.isArray(data) ? data : [];
}







