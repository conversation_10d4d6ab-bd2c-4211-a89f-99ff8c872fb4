export const locationValues = {
    "+***********": {
        businessNumber:"+***********",
        calendarId: '<EMAIL>',
        collectionId: "sms-lwr",
        stripeLocationId:"lwr",
        location: "Lawrence/KC and the surrounding areas",
        link: "https://www.detailongo.com/#lwrphone",
        introMp3:"https://blue-cat-8947.twil.io/assets/intro-lwr.mp3",
        employeeEmail:"<EMAIL>",
        employeeName:"<PERSON>",
        employeeNumber:"+***********",
        reviewLink:"https://g.page/r/CS98X9jMS0IREBM/review",
        branchLocation:"197 Pinecone Dr, Lawrence, KS 66046"
    },
    "+***********": {
        businessNumber:"+***********",
        calendarId: '<EMAIL>',
        collectionId: "sms-e-stl",
        stripeLocationId:"e-stl",
        location: "St. Louis and the surrounding areas",
        link: "https://www.detailongo.com/#stlphone",
        introMp3:"https://blue-cat-8947.twil.io/assets/intro-stl.mp3",
        employeeEmail:"<EMAIL>",
        employeeName:"Taylor Woods",
        employeeNumber:"+3143689339",
        reviewLink:"https://g.page/r/CaNuJ0ypIXA7EBM/review",
        branchLocation:"9220 Litzsinger Rd, St. Louis, MO 63144"

    },
    "+***********": {
        businessNumber:"+***********",
        calendarId: '<EMAIL>',
        collectionId: "sms-la",
        stripeLocationId:"la",
        location: "Los Angeles and the surrounding areas",
        link: "https://www.detailongo.com/#laphone",
        introMp3:"https://blue-cat-8947.twil.io/assets/intro-la.mp3",
        employeeEmail:"<EMAIL>",
        employeeName:"Jenny Biz",
        employeeNumber:"+***********",
        reviewLink:"https://g.page/r/CY3Q12skg1wsEBM/review",
        branchLocation:"Angels Point Rd, Los Angeles, CA 90012"
    },
    "+***********": {
        businessNumber:"+***********",
        calendarId: '<EMAIL>',
        collectionId: "sms-dvr",
        stripeLocationId:"dvr",
        location: "Denver and the surrounding areas",
        link: "https://www.detailongo.com/#dvrphone",
        introMp3:"https://blue-cat-8947.twil.io/assets/intro-dvr.mp3",
        employeeEmail:"<EMAIL>",
        employeeName:"Levi Taylor",
        employeeNumber:"+***********",
        reviewLink:"https://g.page/r/CY3Q12skg1wsEBM/review",
        branchLocation:"7947 Downing St, Thornton, CO 80229"
    },
    "+***********": {
        businessNumber:"+***********",
        calendarId: '<EMAIL>',
        collectionId: "sms-ny",
        stripeLocationId:"ny",
        location: "New York and the surrounding areas",
        link: "https://www.detailongo.com/#nyphone",
        introMp3:"https://blue-cat-8947.twil.io/assets/intro-ny.mp3",
        employeeEmail:"<EMAIL>",
        employeeName:"Levi Taylor",
        employeeNumber:"+***********",
        reviewLink:"https://g.page/r/CTVgBNWsI5ZKEBM/review",
        branchLocation:"32-06 30th Ave., Astoria, NY 11102"
    },
  };