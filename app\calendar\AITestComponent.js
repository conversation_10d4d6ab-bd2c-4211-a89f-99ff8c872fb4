'use client';

import React, { useState } from 'react';
import { testEvents, runAllTests, generateTestReport } from './test-ai-integration';
import { generateJobCompletionData } from './aiJobCompletion';

const AITestComponent = () => {
  const [testResults, setTestResults] = useState(null);
  const [isRunning, setIsRunning] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [singleTestResult, setSingleTestResult] = useState(null);

  const runFullTestSuite = async () => {
    setIsRunning(true);
    setTestResults(null);
    
    try {
      const results = await runAllTests();
      const report = generateTestReport(results);
      setTestResults(report);
    } catch (error) {
      console.error('Test suite failed:', error);
      alert(`Test suite failed: ${error.message}`);
    } finally {
      setIsRunning(false);
    }
  };

  const testSingleEvent = async (event) => {
    setSelectedEvent(event);
    setSingleTestResult(null);
    
    try {
      const result = await generateJobCompletionData(event);
      setSingleTestResult(result);
    } catch (error) {
      console.error('Single test failed:', error);
      setSingleTestResult({ error: error.message });
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6 text-gray-900">AI Job Completion Test Suite</h2>
      
      {/* Full Test Suite */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Full Test Suite</h3>
        <button
          onClick={runFullTestSuite}
          disabled={isRunning}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:bg-blue-300"
        >
          {isRunning ? 'Running Tests...' : 'Run All Tests'}
        </button>
        
        {testResults && (
          <div className="mt-4 p-4 bg-gray-50 rounded">
            <h4 className="font-semibold mb-2">Test Results Summary</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>Total Tests: {testResults.totalTests}</div>
              <div>Successful: {testResults.successfulTests}</div>
              <div>Failed: {testResults.failedTests}</div>
              <div>Average Score: {testResults.averageScore}%</div>
            </div>
            
            <div className="mt-4">
              <h5 className="font-medium mb-2">Detailed Results:</h5>
              {testResults.details.map((result, idx) => (
                <div key={idx} className="mb-2 p-2 bg-white rounded border">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Event {result.eventId}</span>
                    <span className={`px-2 py-1 rounded text-xs ${
                      result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {result.success ? `${result.score}% match` : 'Failed'}
                    </span>
                  </div>
                  {result.error && (
                    <div className="text-red-600 text-xs mt-1">{result.error}</div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Individual Event Testing */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Test Individual Events</h3>
        <div className="grid gap-2">
          {testEvents.map((event) => (
            <button
              key={event.id}
              onClick={() => testSingleEvent(event)}
              className="text-left p-3 border rounded hover:bg-gray-50"
            >
              <div className="font-medium">{event.title}</div>
              <div className="text-sm text-gray-600">
                {new Date(event.start).toLocaleString()} - {event.location}
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Single Test Result */}
      {selectedEvent && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-4">
            Test Result for: {selectedEvent.title}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Event Data */}
            <div className="p-4 bg-gray-50 rounded">
              <h4 className="font-medium mb-2">Event Data</h4>
              <div className="text-sm space-y-1">
                <div><strong>Title:</strong> {selectedEvent.title}</div>
                <div><strong>Location:</strong> {selectedEvent.location}</div>
                <div><strong>Start:</strong> {new Date(selectedEvent.start).toLocaleString()}</div>
                <div><strong>End:</strong> {new Date(selectedEvent.end).toLocaleString()}</div>
                <div><strong>Description:</strong></div>
                <div className="whitespace-pre-wrap text-xs bg-white p-2 rounded">
                  {selectedEvent.description}
                </div>
              </div>
            </div>

            {/* AI Result */}
            <div className="p-4 bg-gray-50 rounded">
              <h4 className="font-medium mb-2">AI Extraction Result</h4>
              {singleTestResult ? (
                singleTestResult.error ? (
                  <div className="text-red-600">{singleTestResult.error}</div>
                ) : (
                  <div className="text-sm space-y-1">
                    {Object.entries(singleTestResult.jobData || {}).map(([key, value]) => (
                      <div key={key}>
                        <strong>{key}:</strong> {value || '(empty)'}
                      </div>
                    ))}
                  </div>
                )
              ) : (
                <div className="text-gray-500">Click an event above to test AI extraction</div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Usage Instructions */}
      <div className="mt-8 p-4 bg-blue-50 rounded">
        <h3 className="text-lg font-semibold mb-2">Usage Instructions</h3>
        <ul className="text-sm space-y-1 list-disc list-inside">
          <li>Use "Run All Tests" to validate AI accuracy across multiple event formats</li>
          <li>Click individual events to see detailed AI extraction results</li>
          <li>Check the console for detailed logging and debugging information</li>
          <li>Modify test events in test-ai-integration.js to test different scenarios</li>
          <li>Expected accuracy should be above 80% for production use</li>
        </ul>
      </div>
    </div>
  );
};

export default AITestComponent;
