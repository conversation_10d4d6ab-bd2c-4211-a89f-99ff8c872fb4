'use client';

import React, { useState, useRef, useEffect } from 'react';
import { generateJobCompletionData, processAIJobData } from './aiJobCompletion';
import { db, storage } from '../../lib/firebase/firebase';
import { collection, addDoc, Timestamp, doc, getDoc } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { onAuthStateChangedListener } from '../../auth';
import { locationValues } from '../booking/location';

const FinishJobPopup = ({ isOpen, onClose, selectedEvent, onJobFinished }) => {
  const [formData, setFormData] = useState({
    vehicle: '',
    customerName: '',
    customerPhone: '',
    customerEmail: '',
    customerAddress: '',
    package: '',
    paymentCollected: 'Card',
    notes: '',
    photos: []
  });
  const [isAILoading, setIsAILoading] = useState(false);
  const [hasAutoFilled, setHasAutoFilled] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [user, setUser] = useState(null);
  const [userDoc, setUserDoc] = useState(null);

  const fileInputRef = useRef(null);

  // Get current user and user document
  useEffect(() => {
    const unsubscribe = onAuthStateChangedListener(async (currentUser) => {
      setUser(currentUser);
      if (currentUser) {
        try {
          const userDocRef = doc(db, 'users', currentUser.uid);
          const userDocSnap = await getDoc(userDocRef);
          if (userDocSnap.exists()) {
            setUserDoc(userDocSnap.data());
          }
        } catch (error) {
          console.error('Error fetching user document:', error);
        }
      }
    });

    return () => unsubscribe();
  }, []);

  // Test Firebase connection
  const testFirebaseConnection = async () => {
    try {
      console.log('🔥 Testing Firebase connection...');
      console.log('🔥 Database instance:', db);
      console.log('🔥 User:', user);

      const testDoc = {
        test: true,
        timestamp: Timestamp.fromDate(new Date()),
        user: user?.uid || 'anonymous'
      };

      const docRef = await addDoc(collection(db, 'test-connection'), testDoc);
      console.log('🔥 ✅ Firebase test successful! Document ID:', docRef.id);
      alert('Firebase connection test successful!');
    } catch (error) {
      console.error('🔥 ❌ Firebase test failed:', error);
      alert(`Firebase test failed: ${error.message}`);
    }
  };

  const handleAIAutoFill = async (isAutomatic = false) => {
    console.log('handleAIAutoFill called', { isAutomatic, selectedEvent, hasAutoFilled });

    if (!selectedEvent) {
      if (!isAutomatic) {
        alert('No event selected for AI analysis');
      }
      return;
    }

    setIsAILoading(true);
    setHasAutoFilled(true);

    try {
      console.log('Calling generateJobCompletionData with:', selectedEvent);
      const result = await generateJobCompletionData(selectedEvent);
      console.log('AI result:', result);

      if (result.success && result.jobData) {
        const processedData = processAIJobData(result.jobData, selectedEvent);
        console.log('Processed data:', processedData);

        // Update form data with AI-generated data, but preserve existing user input
        setFormData(prev => ({
          ...prev,
          vehicle: prev.vehicle || processedData.vehicle || '',
          customerName: prev.customerName || processedData.customerName || '',
          customerPhone: prev.customerPhone || processedData.customerPhone || '',
          customerEmail: prev.customerEmail || processedData.customerEmail || '',
          customerAddress: prev.customerAddress || processedData.customerAddress || '',
          package: prev.package || processedData.package || '',
          paymentCollected: prev.paymentCollected || processedData.paymentCollected || 'Card',
          notes: prev.notes || processedData.notes || ''
        }));

        // Show success message with what was filled (only for manual triggers)
        if (!isAutomatic) {
          const filledFields = Object.entries(processedData)
            .filter(([key, value]) => value && value.trim())
            .map(([key]) => key)
            .join(', ');

          if (filledFields) {
            alert(`AI successfully filled: ${filledFields}`);
          } else {
            alert('AI analysis completed, but no additional information could be extracted from the event.');
          }
        }
      } else if (!isAutomatic) {
        alert('AI analysis completed, but no data could be extracted from the event.');
      }
    } catch (error) {
      console.error('AI auto-fill error:', error);
      if (!isAutomatic) {
        alert(`AI auto-fill failed: ${error.message}`);
      }
    } finally {
      setIsAILoading(false);
    }
  };

  // Automatically trigger AI analysis when popup opens with a new event
  useEffect(() => {
    console.log('useEffect triggered', { isOpen, selectedEvent, hasAutoFilled });

    if (isOpen && selectedEvent && !hasAutoFilled) {
      // Check if the event has enough data to warrant AI analysis
      const hasEventData = selectedEvent.title || selectedEvent.description || selectedEvent.location;
      console.log('Event data check:', { hasEventData, title: selectedEvent.title, description: selectedEvent.description, location: selectedEvent.location });

      if (hasEventData) {
        console.log('Scheduling AI auto-fill...');
        // Delay the auto-fill slightly to allow the popup to render
        const timer = setTimeout(() => {
          console.log('Executing scheduled AI auto-fill');
          handleAIAutoFill(true); // Pass true to indicate this is automatic
        }, 500);

        return () => clearTimeout(timer);
      }
    }
  }, [isOpen, selectedEvent, hasAutoFilled]);

  // Reset auto-fill flag when popup closes
  useEffect(() => {
    if (!isOpen) {
      setHasAutoFilled(false);
    }
  }, [isOpen]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePhotoUpload = (event) => {
    const files = Array.from(event.target.files);
    const photoPromises = files.map(file => {
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          resolve({
            file,
            preview: e.target.result,
            name: file.name,
            size: file.size
          });
        };
        reader.readAsDataURL(file);
      });
    });

    Promise.all(photoPromises).then(photos => {
      setFormData(prev => ({
        ...prev,
        photos: [...prev.photos, ...photos]
      }));
    });
  };

  const removePhoto = (index) => {
    setFormData(prev => ({
      ...prev,
      photos: prev.photos.filter((_, i) => i !== index)
    }));
  };

  // Upload photos to Firebase Storage
  const uploadPhotosToStorage = async (photos) => {
    console.log('📸 Starting photo upload process, photos count:', photos?.length || 0);

    if (!photos || photos.length === 0) {
      console.log('📸 No photos to upload');
      return [];
    }

    try {
      console.log('📸 Using storage instance:', storage);

      const uploadPromises = photos.map(async (photo, index) => {
        try {
          console.log(`📸 Uploading photo ${index + 1}:`, photo.name);

          // Create a unique filename
          const timestamp = new Date().getTime();
          const fileName = `finished-jobs/${selectedEvent?.id || 'unknown'}_${timestamp}_${index}.jpg`;
          const storageRef = ref(storage, fileName);

          console.log(`📸 Storage reference created:`, fileName);

          // Convert base64 to blob
          const response = await fetch(photo.preview);
          const blob = await response.blob();

          console.log(`📸 Blob created, size:`, blob.size);

          // Upload to Firebase Storage
          const snapshot = await uploadBytes(storageRef, blob);
          const downloadURL = await getDownloadURL(snapshot.ref);

          console.log(`📸 Photo ${index + 1} uploaded successfully:`, downloadURL);

          return {
            name: photo.name,
            size: photo.size,
            url: downloadURL,
            path: fileName
          };
        } catch (error) {
          console.error(`📸 Error uploading photo ${index + 1}:`, error);
          return null;
        }
      });

      const uploadedPhotos = await Promise.all(uploadPromises);
      const successfulUploads = uploadedPhotos.filter(photo => photo !== null);

      console.log('📸 Photo upload complete. Successful uploads:', successfulUploads.length);
      return successfulUploads;

    } catch (error) {
      console.error('📸 Photo upload process failed:', error);
      return [];
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (isSubmitting) return;

    setIsSubmitting(true);

    try {
      // Check if user is authenticated
      if (!user) {
        throw new Error('User not authenticated. Please log in and try again.');
      }

      // Upload photos to Firebase Storage first
      console.log('Uploading photos...');
      const uploadedPhotos = await uploadPhotosToStorage(formData.photos);
      console.log('Photos uploaded:', uploadedPhotos);

      // Prepare job completion data
      const now = new Date();
      const jobData = {
        // Form data (with empty strings if not filled)
        vehicle: formData.vehicle || '',
        customerName: formData.customerName || '',
        customerPhone: formData.customerPhone || '',
        customerEmail: formData.customerEmail || '',
        customerAddress: formData.customerAddress || '',
        package: formData.package || '',
        paymentCollected: formData.paymentCollected || 'Card',
        notes: formData.notes || '',

        // Event details
        eventId: selectedEvent?.id || '',
        eventTitle: selectedEvent?.title || '',
        eventLocation: selectedEvent?.location || '',
        eventStart: selectedEvent?.start ? Timestamp.fromDate(new Date(selectedEvent.start)) : null,
        eventEnd: selectedEvent?.end ? Timestamp.fromDate(new Date(selectedEvent.end)) : null,
        eventDescription: selectedEvent?.description || '',

        // Metadata
        completedAt: Timestamp.fromDate(now),
        submittedAt: Timestamp.fromDate(now),
        submittedBy: user?.uid || '',
        submittedByName: user?.displayName || '',
        submittedByEmail: user?.email || '',

        // Branch/Location Information
        branch: userDoc?.branch || '',
        branchLocation: userDoc?.branch || '',
        branchRequested: userDoc?.branch?.includes('-requested') || false,
        branchClean: userDoc?.branch?.replace('-requested', '') || '',

        // Enhanced location details from locationValues
        locationDetails: (() => {
          const branchClean = userDoc?.branch?.replace('-requested', '') || '';
          const locationData = Object.values(locationValues).find(loc =>
            loc.stripeLocationId === branchClean ||
            loc.location.toLowerCase().includes(branchClean)
          );
          return locationData ? {
            businessNumber: locationData.businessNumber,
            location: locationData.location,
            employeeName: locationData.employeeName,
            employeeEmail: locationData.employeeEmail,
            branchLocation: locationData.branchLocation,
            stripeLocationId: locationData.stripeLocationId
          } : null;
        })(),

        // Photos
        photos: uploadedPhotos,
        photoCount: uploadedPhotos.length,

        // Additional context
        todaysDate: now.toLocaleDateString(),
        todaysDateTime: now.toLocaleString(),

        // AI metadata
        wasAIUsed: hasAutoFilled,
        aiAnalysisDate: hasAutoFilled ? Timestamp.fromDate(now) : null
      };

      console.log('Attempting to save job data to Firestore:', jobData);
      console.log('Database instance:', db);
      console.log('User:', user);
      console.log('UserDoc:', userDoc);

      // Save to Firestore
      try {
        const docRef = await addDoc(collection(db, 'finished-jobs'), jobData);
        console.log('✅ Job saved successfully with ID:', docRef.id);

        // Call the original callback
        onJobFinished({
          ...jobData,
          firestoreId: docRef.id
        });

        alert('Job completion saved successfully to Firebase!');
      } catch (firestoreError) {
        console.error('❌ Firestore save error:', firestoreError);
        console.error('Error details:', {
          code: firestoreError.code,
          message: firestoreError.message,
          stack: firestoreError.stack
        });

        // Still call the callback with the data (for backward compatibility)
        onJobFinished(jobData);

        alert(`Error saving to Firebase: ${firestoreError.message}`);
        throw firestoreError;
      }

    } catch (error) {
      console.error('Error saving job completion:', error);
      alert(`Error saving job completion: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setFormData({
      vehicle: '',
      customerName: '',
      customerPhone: '',
      customerEmail: '',
      customerAddress: '',
      package: '',
      paymentCollected: 'Card',
      notes: '',
      photos: []
    });
  };



  const handleClose = () => {
    if (isSubmitting) return; // Prevent closing while submitting
    resetForm();
    setHasAutoFilled(false);
    setIsAILoading(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-800 bg-opacity-50 flex justify-center items-center z-50">
      <div className="bg-white p-6 rounded-lg w-[90%] max-w-2xl shadow-lg relative max-h-[80vh] overflow-y-auto">
        <button 
          onClick={handleClose} 
          className="absolute top-3 right-3 text-2xl hover:text-gray-600"
        >
          &times;
        </button>
        
        <h3 className="text-xl font-semibold mb-4 text-gray-900">Finish Job</h3>
        
        {selectedEvent && (
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-700">Event Details:</h4>
            <p className="text-sm text-gray-600">{selectedEvent.title}</p>
            {selectedEvent.location && (
              <p className="text-sm text-gray-600">Location: {selectedEvent.location}</p>
            )}
            {selectedEvent.start && (
              <p className="text-sm text-gray-600">
                Date: {new Date(selectedEvent.start).toLocaleString()}
              </p>
            )}
          </div>
        )}



        {/* AI Auto-Fill Button */}
        {selectedEvent && (
          <div className="mb-4">
            <button
              type="button"
              onClick={() => handleAIAutoFill(false)}
              disabled={isAILoading}
              className="w-full bg-purple-500 text-white px-4 py-2 rounded-md hover:bg-purple-600 focus:ring-2 focus:ring-purple-500 disabled:bg-purple-300 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {isAILoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  {hasAutoFilled ? 'Auto-Analyzing Event...' : 'Analyzing Event...'}
                </>
              ) : (
                <>
                  🤖 {hasAutoFilled ? 'Re-run AI Auto-Fill' : 'AI Auto-Fill from Event'}
                </>
              )}
            </button>
            <p className="text-xs text-gray-500 mt-1 text-center">
              {hasAutoFilled
                ? 'AI has already analyzed this event. Click to re-run analysis.'
                : 'AI will analyze the event details and auto-fill the form fields below'
              }
            </p>
          </div>
        )}

        {/* Information Note */}
        <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <p className="text-sm text-blue-800">
            <strong>Note:</strong> All event details, timestamps, and your information will be automatically saved.
            Fill out the fields below as needed - empty fields will be saved as blank entries.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Vehicle - Optional */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Vehicle
            </label>
            <input
              type="text"
              value={formData.vehicle}
              onChange={(e) => handleInputChange('vehicle', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter vehicle information (optional)"
            />
          </div>

          {/* Customer Name - Optional */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Customer Name
            </label>
            <input
              type="text"
              value={formData.customerName}
              onChange={(e) => handleInputChange('customerName', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter customer name"
            />
          </div>

          {/* Customer Phone - Optional */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Customer Phone
            </label>
            <input
              type="tel"
              value={formData.customerPhone}
              onChange={(e) => handleInputChange('customerPhone', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter customer phone"
            />
          </div>

          {/* Customer Email - Optional */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Customer Email
            </label>
            <input
              type="email"
              value={formData.customerEmail}
              onChange={(e) => handleInputChange('customerEmail', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter customer email"
            />
          </div>

          {/* Customer Address - Optional */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Customer Address
            </label>
            <textarea
              value={formData.customerAddress}
              onChange={(e) => handleInputChange('customerAddress', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter customer address"
              rows="2"
            />
          </div>

          {/* Package - Optional */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Package
            </label>
            <input
              type="text"
              value={formData.package}
              onChange={(e) => handleInputChange('package', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter package details"
            />
          </div>

          {/* Payment Collected */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Payment Collected
            </label>
            <select
              value={formData.paymentCollected}
              onChange={(e) => handleInputChange('paymentCollected', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="Card">Card</option>
              <option value="Cash">Cash</option>
              <option value="Check">Check</option>
              <option value="Other">Other</option>
            </select>
          </div>

          {/* Photo Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Photos
            </label>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handlePhotoUpload}
              multiple
              accept="image/*"
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            
            {/* Photo Previews */}
            {formData.photos.length > 0 && (
              <div className="mt-2 grid grid-cols-2 sm:grid-cols-3 gap-2">
                {formData.photos.map((photo, index) => (
                  <div key={index} className="relative">
                    <img
                      src={photo.preview}
                      alt={`Preview ${index + 1}`}
                      className="w-full h-20 object-cover rounded border"
                    />
                    <button
                      type="button"
                      onClick={() => removePhoto(index)}
                      className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs hover:bg-red-600"
                    >
                      ×
                    </button>
                    <p className="text-xs text-gray-500 mt-1 truncate">{photo.name}</p>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter any additional notes"
              rows="3"
            />
          </div>

          {/* Submit Buttons */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:ring-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:ring-2 focus:ring-green-500 disabled:bg-green-300 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Saving...
                </>
              ) : (
                'Finish Job'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default FinishJobPopup;
