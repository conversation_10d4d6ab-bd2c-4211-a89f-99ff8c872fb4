'use client';

import React, { useState, useEffect } from 'react';

const PastChargesPopup = ({ visible, onClose, customerId }) => {
    const [charges, setCharges] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);

    useEffect(() => {
        if (!visible || !customerId) return;

        const fetchCharges = async () => {
            setIsLoading(true);
            setError(null);
            try {
                const response = await fetch(
                    `https://previous-payments-896343340170.us-central1.run.app?customerId=${customerId}`
                );
                const data = await response.json();
                if (!response.ok) throw new Error(data.error?.message || 'Failed to fetch charges');
                setCharges(data.charges || []);
            } catch (err) {
                setError(err.message);
                setCharges([]);
            } finally {
                setIsLoading(false);
            }
        };

        fetchCharges();
    }, [visible, customerId]);

    const calculateDaysAgo = (timestamp) => {
        const today = new Date();
        const chargeDate = new Date(timestamp * 1000);
        const diffTime = today - chargeDate;
        const diffDays = Math.max(0, Math.floor(diffTime / (1000 * 60 * 60 * 24)));
        return diffDays;
    };

    const getDaysAgoColor = (days) => {
        if (days <= 7) return 'bg-green-100';
        if (days <= 30) return 'bg-yellow-100';
        return 'bg-red-100';
    };

    if (!visible) return null;

    return (
        <div className="fixed inset-0 bg-gray-800 bg-opacity-50 flex justify-center items-center z-50">
            <div className="relative bg-white p-6 rounded-lg w-[90%] max-w-4xl shadow-lg max-h-[80vh] overflow-y-auto">
                <button
                    onClick={onClose}
                    className="absolute top-3 right-3 rounded-lg px-4 py-2 bg-red-500 hover:bg-red-600 text-white"
                >
                    Close
                </button>

                <h3 className="text-xl font-semibold mb-4">Past Charges for Customer</h3>

                {isLoading ? (
                    <p className="text-gray-600">Loading charges...</p>
                ) : error ? (
                    <p className="text-red-500">Error: {error}</p>
                ) : charges.length === 0 ? (
                    <p className="text-gray-600">No charges found for this customer.</p>
                ) : (
                    <div className="overflow-x-auto">
                        <table className="w-full table-auto border-collapse">
                            <thead>
                                <tr className="bg-gray-100">
                                    <th className="px-4 py-2 text-left text-gray-800">Date</th>
                                    <th className="px-4 py-2 text-left text-gray-800">Days Ago</th>
                                    <th className="px-4 py-2 text-left text-gray-800">Amount</th>
                                    <th className="px-4 py-2 text-left text-gray-800">Description</th>
                                    <th className="px-4 py-2 text-left text-gray-800">Payment Method</th>
                                    <th className="px-4 py-2 text-left text-gray-800">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {charges.map((charge) => {
                                    const daysAgo = calculateDaysAgo(charge.created);
                                    return (
                                        <tr key={charge.id} className="border-t">
                                            <td className="px-4 py-2">
                                                {new Date(charge.created * 1000).toLocaleDateString()}
                                            </td>
                                            <td className={`px-4 py-2 ${getDaysAgoColor(daysAgo)}`}>
                                                {daysAgo} days ago
                                            </td>
                                            <td className="px-4 py-2">
                                                ${(charge.amount / 100).toFixed(2)}
                                            </td>
                                            <td className="px-4 py-2">{charge.description || 'N/A'}</td>
                                            <td className="px-4 py-2">
                                                {charge.payment_method_details?.card
                                                    ? `${charge.payment_method_details.card.brand.toUpperCase()} ****${charge.payment_method_details.card.last4}`
                                                    : charge.payment_method_details?.type || 'Unknown'}
                                            </td>
                                            <td className="px-4 py-2">{charge.status}</td>
                                        </tr>
                                    );
                                })}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>
        </div>
    );
};

export default PastChargesPopup;