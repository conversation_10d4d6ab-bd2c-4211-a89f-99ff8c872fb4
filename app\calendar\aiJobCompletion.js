/**
 * AI Job Completion Utility Functions
 * Handles the integration between calendar events and AI-powered job completion form filling
 */

/**
 * Calls the AI service to analyze a calendar event and generate job completion data
 * @param {Object} eventData - The calendar event data
 * @returns {Promise<Object>} - The AI-generated job completion data
 */
export async function generateJobCompletionData(eventData) {
    try {
        const response = await fetch('/api/ai-job-completion', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ eventData }),
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to generate job completion data');
        }

        const result = await response.json();
        return result;
    } catch (error) {
        console.error('Error generating job completion data:', error);
        throw error;
    }
}

/**
 * Extracts basic information from calendar event description using regex patterns
 * This serves as a fallback when AI service is unavailable
 * @param {string} description - The event description
 * @returns {Object} - Extracted information
 */
export function extractBasicEventInfo(description) {
    if (!description) return {};

    const extracted = {};

    // Extract phone numbers
    const phoneRegex = /(?:Phone|Tel|Mobile|Cell):\s*([+]?[\d\s\-\(\)]+)/i;
    const phoneMatch = description.match(phoneRegex);
    if (phoneMatch) {
        extracted.customerPhone = phoneMatch[1].trim();
    }

    // Extract email addresses
    const emailRegex = /(?:Email):\s*([^\s]+@[^\s]+)/i;
    const emailMatch = description.match(emailRegex);
    if (emailMatch) {
        extracted.customerEmail = emailMatch[1].trim();
    }

    // Extract vehicle information
    const vehicleRegex = /(?:Vehicle|Car):\s*([^\n\r]+)/i;
    const vehicleMatch = description.match(vehicleRegex);
    if (vehicleMatch) {
        extracted.vehicle = vehicleMatch[1].trim();
    }

    // Extract package/service information
    const packageRegex = /(?:Package|Service):\s*([^\n\r]+)/i;
    const packageMatch = description.match(packageRegex);
    if (packageMatch) {
        extracted.package = packageMatch[1].trim();
    }

    // Extract address
    const addressRegex = /(?:Address|Location):\s*([^\n\r]+)/i;
    const addressMatch = description.match(addressRegex);
    if (addressMatch) {
        extracted.customerAddress = addressMatch[1].trim();
    }

    return extracted;
}

/**
 * Determines the likely service package based on event duration and keywords
 * @param {number} durationHours - Event duration in hours
 * @param {string} title - Event title
 * @param {string} description - Event description
 * @returns {string} - Suggested package
 */
export function inferServicePackage(durationHours, title = '', description = '') {
    const text = `${title} ${description}`.toLowerCase();

    // Check for specific package keywords
    if (text.includes('interior') && text.includes('exterior')) {
        return 'Interior + Exterior Combo';
    }
    if (text.includes('combo')) {
        return 'Interior + Exterior Combo';
    }
    if (text.includes('interior')) {
        return 'Interior Detailing';
    }
    if (text.includes('exterior')) {
        return 'Exterior Detailing';
    }
    if (text.includes('wash')) {
        return 'Exterior Wash';
    }

    // Infer based on duration
    if (durationHours >= 3) {
        return 'Interior + Exterior Combo';
    } else if (durationHours >= 2) {
        return 'Interior Detailing';
    } else {
        return 'Exterior Detailing';
    }
}

/**
 * Validates and cleans phone number format
 * @param {string} phone - Raw phone number
 * @returns {string} - Cleaned phone number
 */
export function cleanPhoneNumber(phone) {
    if (!phone) return '';
    
    // Remove all non-digit characters except +
    const cleaned = phone.replace(/[^\d+]/g, '');
    
    // Add +1 prefix for US numbers if not present
    if (cleaned.length === 10) {
        return `+1${cleaned}`;
    }
    if (cleaned.length === 11 && cleaned.startsWith('1')) {
        return `+${cleaned}`;
    }
    if (cleaned.startsWith('+')) {
        return cleaned;
    }
    
    return phone; // Return original if can't clean
}

/**
 * Validates email format
 * @param {string} email - Email address
 * @returns {boolean} - Whether email is valid
 */
export function isValidEmail(email) {
    if (!email) return false;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Checks if an event has ended and should trigger automatic job completion
 * @param {Object} eventData - The calendar event data
 * @returns {boolean} - Whether the event has ended
 */
export function shouldTriggerAutoCompletion(eventData) {
    if (!eventData || !eventData.end) return false;

    const now = new Date();
    const eventEnd = new Date(eventData.end);

    // Event has ended
    return eventEnd <= now;
}

/**
 * Checks if an event is currently in progress
 * @param {Object} eventData - The calendar event data
 * @returns {boolean} - Whether the event is currently happening
 */
export function isEventInProgress(eventData) {
    if (!eventData || !eventData.start || !eventData.end) return false;

    const now = new Date();
    const eventStart = new Date(eventData.start);
    const eventEnd = new Date(eventData.end);

    return eventStart <= now && now <= eventEnd;
}

/**
 * Gets a user-friendly status for an event
 * @param {Object} eventData - The calendar event data
 * @returns {string} - Event status ('upcoming', 'in-progress', 'completed')
 */
export function getEventStatus(eventData) {
    if (!eventData || !eventData.start) return 'unknown';

    const now = new Date();
    const eventStart = new Date(eventData.start);
    const eventEnd = eventData.end ? new Date(eventData.end) : null;

    if (eventStart > now) {
        return 'upcoming';
    } else if (eventEnd && eventEnd <= now) {
        return 'completed';
    } else {
        return 'in-progress';
    }
}

/**
 * Processes AI-generated job data and applies additional validation/cleaning
 * @param {Object} aiJobData - Raw AI-generated job data
 * @param {Object} eventData - Original event data for fallback
 * @returns {Object} - Processed and validated job data
 */
export function processAIJobData(aiJobData, eventData) {
    const processed = { ...aiJobData };

    // Clean phone number
    if (processed.customerPhone) {
        processed.customerPhone = cleanPhoneNumber(processed.customerPhone);
    }

    // Validate email
    if (processed.customerEmail && !isValidEmail(processed.customerEmail)) {
        processed.customerEmail = '';
    }

    // Use event location as fallback for customer address
    if (!processed.customerAddress && eventData.location) {
        processed.customerAddress = eventData.location;
    }

    // Use event title as fallback for customer name if not extracted
    if (!processed.customerName && eventData.title) {
        // Try to extract name from title (remove common prefixes)
        const cleanTitle = eventData.title
            .replace(/^(Detail|Detailing|Service|Appointment)\s+/i, '')
            .replace(/\s+(Detail|Detailing|Service|Appointment)$/i, '')
            .trim();
        if (cleanTitle && !cleanTitle.includes('@') && !cleanTitle.includes('http')) {
            processed.customerName = cleanTitle;
        }
    }

    return processed;
}
