/**
 * Test file for AI Job Completion Integration
 * This file contains test cases and examples for validating the AI integration
 */

// Example calendar events for testing
export const testEvents = [
  {
    id: 'test-1',
    title: '<PERSON> - Detail Service',
    description: `Customer: <PERSON>
Phone: (*************
Email: <EMAIL>
Vehicle: 2020 Honda CR-V
Service: Interior + Exterior Combo
Address: 123 Main St, Lawrence, KS 66046
Notes: Customer mentioned pet hair removal needed`,
    location: '123 Main St, Lawrence, KS 66046',
    start: new Date('2024-01-15T09:00:00'),
    end: new Date('2024-01-15T12:00:00')
  },
  
  {
    id: 'test-2',
    title: '<PERSON>',
    description: `Detail appointment for <PERSON>
Contact: <EMAIL>
Phone: ******-987-6543
2018 Toyota Camry - Sedan
Interior detailing only
Location: 456 Oak Ave, Topeka, KS 66604`,
    location: '456 Oak Ave, Topeka, KS 66604',
    start: new Date('2024-01-15T14:00:00'),
    end: new Date('2024-01-15T16:30:00')
  },
  
  {
    id: 'test-3',
    title: '<PERSON> - 2021 Ford F-150',
    description: `Exterior wash and wax
Customer phone: ************
Truck is very dirty from construction work
Payment: Cash preferred`,
    location: '789 Pine St, Manhattan, KS 66502',
    start: new Date('2024-01-16T10:00:00'),
    end: new Date('2024-01-16T11:30:00')
  },
  
  {
    id: 'test-4',
    title: 'Detail Appointment',
    description: `Customer: Lisa Chen
Vehicle: 2019 Subaru Outback
Service: Full detail (interior + exterior)
Phone: (*************
Email: <EMAIL>
Special request: Remove coffee stains from seats`,
    location: 'Customer Driveway - 321 Elm St, Lawrence, KS',
    start: new Date('2024-01-16T13:00:00'),
    end: new Date('2024-01-16T16:00:00')
  },
  
  {
    id: 'test-5',
    title: 'Robert Wilson - Quick Wash',
    description: `Basic exterior wash
2022 BMW X5
Contact: <EMAIL>
Phone: ************`,
    location: '555 Maple Dr, Overland Park, KS',
    start: new Date('2024-01-17T08:00:00'),
    end: new Date('2024-01-17T09:00:00')
  }
];

// Expected AI extraction results for validation
export const expectedResults = {
  'test-1': {
    vehicle: '2020 Honda CR-V',
    customerName: 'John Smith',
    customerPhone: '+1555123456',
    customerEmail: '<EMAIL>',
    customerAddress: '123 Main St, Lawrence, KS 66046',
    package: 'Interior + Exterior Combo',
    notes: 'Customer mentioned pet hair removal needed'
  },
  
  'test-2': {
    vehicle: '2018 Toyota Camry',
    customerName: 'Sarah Johnson',
    customerPhone: '+15559876543',
    customerEmail: '<EMAIL>',
    customerAddress: '456 Oak Ave, Topeka, KS 66604',
    package: 'Interior Detailing'
  },
  
  'test-3': {
    vehicle: '2021 Ford F-150',
    customerName: 'Mike Davis',
    customerPhone: '+15554443333',
    customerAddress: '789 Pine St, Manhattan, KS 66502',
    package: 'Exterior Detailing',
    paymentCollected: 'Cash',
    notes: 'Truck is very dirty from construction work'
  }
};

/**
 * Test function to validate AI extraction accuracy
 * @param {Object} testEvent - Event to test
 * @param {Object} expectedResult - Expected extraction result
 * @returns {Object} - Test results
 */
export async function testAIExtraction(testEvent, expectedResult) {
  try {
    const response = await fetch('/api/ai-job-completion', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ eventData: testEvent }),
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status}`);
    }

    const result = await response.json();
    
    if (!result.success) {
      throw new Error('AI extraction failed');
    }

    const extracted = result.jobData;
    const testResults = {
      eventId: testEvent.id,
      success: true,
      extracted,
      expected: expectedResult,
      matches: {},
      score: 0
    };

    // Compare extracted data with expected results
    const fieldsToCheck = ['vehicle', 'customerName', 'customerPhone', 'customerEmail', 'customerAddress', 'package', 'paymentCollected', 'notes'];
    let matchCount = 0;
    
    fieldsToCheck.forEach(field => {
      if (expectedResult[field]) {
        const extractedValue = extracted[field]?.toLowerCase().trim() || '';
        const expectedValue = expectedResult[field].toLowerCase().trim();
        
        // For phone numbers, normalize format for comparison
        if (field === 'customerPhone') {
          const normalizePhone = (phone) => phone.replace(/[^\d]/g, '');
          testResults.matches[field] = normalizePhone(extractedValue) === normalizePhone(expectedValue);
        } else {
          // For other fields, check if extracted contains expected or vice versa
          testResults.matches[field] = extractedValue.includes(expectedValue) || expectedValue.includes(extractedValue);
        }
        
        if (testResults.matches[field]) {
          matchCount++;
        }
      }
    });

    testResults.score = (matchCount / Object.keys(expectedResult).length) * 100;
    return testResults;

  } catch (error) {
    return {
      eventId: testEvent.id,
      success: false,
      error: error.message,
      score: 0
    };
  }
}

/**
 * Run all test cases
 * @returns {Promise<Array>} - Array of test results
 */
export async function runAllTests() {
  const results = [];
  
  for (const event of testEvents) {
    const expected = expectedResults[event.id];
    if (expected) {
      const result = await testAIExtraction(event, expected);
      results.push(result);
      
      // Add delay between requests to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  return results;
}

/**
 * Generate a test report
 * @param {Array} testResults - Results from runAllTests()
 * @returns {Object} - Test report summary
 */
export function generateTestReport(testResults) {
  const totalTests = testResults.length;
  const successfulTests = testResults.filter(r => r.success).length;
  const averageScore = testResults.reduce((sum, r) => sum + r.score, 0) / totalTests;
  
  const report = {
    totalTests,
    successfulTests,
    failedTests: totalTests - successfulTests,
    averageScore: Math.round(averageScore * 100) / 100,
    details: testResults
  };
  
  return report;
}

// Console testing functions for development
if (typeof window !== 'undefined') {
  window.testAIIntegration = {
    testEvents,
    expectedResults,
    testAIExtraction,
    runAllTests,
    generateTestReport
  };
}
