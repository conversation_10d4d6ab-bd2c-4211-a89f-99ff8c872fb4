'use client';

import React, { useState, useEffect } from 'react';
import { db } from '../../lib/firebase/firebase';
import { collection, query, where, orderBy, onSnapshot, doc, getDoc } from 'firebase/firestore';
import { onAuthStateChangedListener } from '../../auth';
import { format } from 'date-fns';

const FinishedJobsPage = () => {
  const [jobs, setJobs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [userDoc, setUserDoc] = useState(null);
  const [filter, setFilter] = useState('all'); // all, today, week, month
  const [sortBy, setSortBy] = useState('newest'); // newest, oldest, customer, vehicle
  const [searchTerm, setSearchTerm] = useState('');

  // Export to CSV function
  const exportToCSV = (jobsToExport) => {
    const headers = [
      'Date Completed',
      'Customer Name',
      'Vehicle',
      'Package',
      'Payment Method',
      'Phone',
      'Email',
      'Address',
      'Event Location',
      'Notes',
      'Photos Count',
      'Submitted By',
      'AI Used'
    ];

    const csvData = jobsToExport.map(job => [
      job.completedAt ? format(job.completedAt, 'yyyy-MM-dd HH:mm') : '',
      job.customerName || job.eventTitle || '',
      job.vehicle || '',
      job.package || '',
      job.paymentCollected || '',
      job.customerPhone || '',
      job.customerEmail || '',
      job.customerAddress || '',
      job.eventLocation || '',
      job.notes || '',
      job.photoCount || 0,
      job.submittedByName || '',
      job.wasAIUsed ? 'Yes' : 'No'
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `finished-jobs-${userDoc?.branch}-${format(new Date(), 'yyyy-MM-dd')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Get current user and user document
  useEffect(() => {
    const unsubscribe = onAuthStateChangedListener(async (currentUser) => {
      setUser(currentUser);
      if (currentUser) {
        try {
          const userDocRef = doc(db, 'users', currentUser.uid);
          const userDocSnap = await getDoc(userDocRef);
          if (userDocSnap.exists()) {
            setUserDoc(userDocSnap.data());
          }
        } catch (error) {
          console.error('Error fetching user document:', error);
        }
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // Fetch finished jobs for the user's branch
  useEffect(() => {
    if (!user || !userDoc?.branch) {
      setJobs([]);
      return;
    }

    console.log('Setting up jobs listener for branch:', userDoc.branch);

    // Create query to get jobs for this branch
    const jobsQuery = query(
      collection(db, 'finished-jobs'),
      where('branch', '==', userDoc.branch),
      orderBy('completedAt', 'desc')
    );

    // Set up real-time listener
    const unsubscribe = onSnapshot(
      jobsQuery,
      (snapshot) => {
        const jobsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          // Convert Firestore timestamps to JavaScript dates
          completedAt: doc.data().completedAt?.toDate(),
          submittedAt: doc.data().submittedAt?.toDate(),
          eventStart: doc.data().eventStart?.toDate(),
          eventEnd: doc.data().eventEnd?.toDate(),
        }));
        
        console.log('Fetched jobs:', jobsData.length);
        setJobs(jobsData);
        setLoading(false);
      },
      (error) => {
        console.error('Error fetching jobs:', error);
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, [user, userDoc]);

  // Filter and sort jobs
  const filteredAndSortedJobs = React.useMemo(() => {
    let filtered = jobs;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(job => 
        job.customerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.vehicle?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.eventTitle?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.customerPhone?.includes(searchTerm) ||
        job.customerEmail?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply date filter
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    switch (filter) {
      case 'today':
        filtered = filtered.filter(job => job.completedAt >= today);
        break;
      case 'week':
        filtered = filtered.filter(job => job.completedAt >= weekAgo);
        break;
      case 'month':
        filtered = filtered.filter(job => job.completedAt >= monthAgo);
        break;
      default:
        // 'all' - no additional filtering
        break;
    }

    // Apply sorting
    switch (sortBy) {
      case 'oldest':
        filtered.sort((a, b) => a.completedAt - b.completedAt);
        break;
      case 'customer':
        filtered.sort((a, b) => (a.customerName || a.eventTitle || '').localeCompare(b.customerName || b.eventTitle || ''));
        break;
      case 'vehicle':
        filtered.sort((a, b) => (a.vehicle || '').localeCompare(b.vehicle || ''));
        break;
      default: // 'newest'
        filtered.sort((a, b) => b.completedAt - a.completedAt);
        break;
    }

    return filtered;
  }, [jobs, filter, sortBy, searchTerm]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading finished jobs...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Please log in to view finished jobs.</p>
        </div>
      </div>
    );
  }

  if (!userDoc?.branch) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Please complete your profile setup to view finished jobs.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-16 pb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Finished Jobs</h1>
          <p className="mt-2 text-gray-600">
            Branch: <span className="font-medium">{userDoc.branch}</span> •
            Total Jobs: <span className="font-medium">{filteredAndSortedJobs.length}</span>
          </p>
        </div>

        {/* Stats Summary */}
        {jobs.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-white rounded-lg shadow p-4">
              <div className="text-2xl font-bold text-blue-600">{jobs.length}</div>
              <div className="text-sm text-gray-600">Total Jobs</div>
            </div>
            <div className="bg-white rounded-lg shadow p-4">
              <div className="text-2xl font-bold text-green-600">
                {jobs.filter(job => job.wasAIUsed).length}
              </div>
              <div className="text-sm text-gray-600">AI Assisted</div>
            </div>
            <div className="bg-white rounded-lg shadow p-4">
              <div className="text-2xl font-bold text-purple-600">
                {jobs.filter(job => job.photos && job.photos.length > 0).length}
              </div>
              <div className="text-sm text-gray-600">With Photos</div>
            </div>
            <div className="bg-white rounded-lg shadow p-4">
              <div className="text-2xl font-bold text-orange-600">
                {jobs.filter(job => {
                  const today = new Date();
                  const jobDate = new Date(job.completedAt);
                  return jobDate.toDateString() === today.toDateString();
                }).length}
              </div>
              <div className="text-sm text-gray-600">Today</div>
            </div>
          </div>
        )}

        {/* Filters and Search */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Customer, vehicle, phone..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Date Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Time</option>
                <option value="today">Today</option>
                <option value="week">Last 7 Days</option>
                <option value="month">Last 30 Days</option>
              </select>
            </div>

            {/* Sort */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="customer">Customer Name</option>
                <option value="vehicle">Vehicle</option>
              </select>
            </div>

            {/* Export Button */}
            <div className="flex items-end">
              <button
                onClick={() => exportToCSV(filteredAndSortedJobs)}
                className="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600 text-sm"
                disabled={filteredAndSortedJobs.length === 0}
              >
                📊 Export CSV
              </button>
            </div>
          </div>
        </div>

        {/* Jobs List */}
        {filteredAndSortedJobs.length === 0 ? (
          <div className="bg-white rounded-lg shadow p-8 text-center">
            <p className="text-gray-500">No finished jobs found.</p>
            {searchTerm && (
              <p className="text-sm text-gray-400 mt-2">Try adjusting your search or filters.</p>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {filteredAndSortedJobs.map((job) => (
              <JobCard key={job.id} job={job} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Individual Job Card Component
const JobCard = ({ job }) => {
  const [expanded, setExpanded] = useState(false);

  return (
    <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow">
      <div className="p-6">
        {/* Header */}
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {job.customerName || job.eventTitle || 'Unnamed Job'}
            </h3>
            <p className="text-sm text-gray-600">
              {job.completedAt && format(job.completedAt, 'MMM dd, yyyy • h:mm a')}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {job.wasAIUsed && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                🤖 AI Used
              </span>
            )}
            <button
              onClick={() => setExpanded(!expanded)}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              {expanded ? 'Show Less' : 'Show More'}
            </button>
          </div>
        </div>

        {/* Quick Info */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Vehicle:</span>
            <p className="font-medium">{job.vehicle || 'Not specified'}</p>
          </div>
          <div>
            <span className="text-gray-500">Package:</span>
            <p className="font-medium">{job.package || 'Not specified'}</p>
          </div>
          <div>
            <span className="text-gray-500">Payment:</span>
            <p className="font-medium">{job.paymentCollected || 'Not specified'}</p>
          </div>
          <div>
            <span className="text-gray-500">Photos:</span>
            <p className="font-medium">{job.photoCount || 0} uploaded</p>
          </div>
        </div>

        {/* Expanded Details */}
        {expanded && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Customer Info */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Customer Information</h4>
                <div className="space-y-2 text-sm">
                  <div><span className="text-gray-500">Name:</span> {job.customerName || 'Not provided'}</div>
                  <div><span className="text-gray-500">Phone:</span> {job.customerPhone || 'Not provided'}</div>
                  <div><span className="text-gray-500">Email:</span> {job.customerEmail || 'Not provided'}</div>
                  <div><span className="text-gray-500">Address:</span> {job.customerAddress || 'Not provided'}</div>
                </div>
              </div>

              {/* Event Info */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Event Information</h4>
                <div className="space-y-2 text-sm">
                  <div><span className="text-gray-500">Event ID:</span> {job.eventId || 'Not provided'}</div>
                  <div><span className="text-gray-500">Location:</span> {job.eventLocation || 'Not provided'}</div>
                  {job.eventStart && (
                    <div><span className="text-gray-500">Scheduled:</span> {format(job.eventStart, 'MMM dd, yyyy • h:mm a')}</div>
                  )}
                  <div><span className="text-gray-500">Submitted by:</span> {job.submittedByName || 'Unknown'}</div>
                </div>
              </div>
            </div>

            {/* Notes */}
            {job.notes && (
              <div className="mt-4">
                <h4 className="font-medium text-gray-900 mb-2">Notes</h4>
                <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded">{job.notes}</p>
              </div>
            )}

            {/* Photos */}
            {job.photos && job.photos.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium text-gray-900 mb-2">Photos ({job.photos.length})</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {job.photos.map((photo, index) => (
                    <img
                      key={index}
                      src={photo.url}
                      alt={`Job photo ${index + 1}`}
                      className="w-full h-24 object-cover rounded border cursor-pointer hover:opacity-75"
                      onClick={() => window.open(photo.url, '_blank')}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default FinishedJobsPage;
