@tailwind base;
@tailwind components;
@tailwind utilities;

:root {}

@keyframes gradient-x {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient-x {
  background-size: 200% 200%;
  animation: gradient-x 3s ease infinite;
}

/* Add gradient animation */
@keyframes vertical-gradient {
  0% {
    background-position: 0% 0%;
  }

  50% {
    background-position: 0% 100%;
  }

  100% {
    background-position: 0% 0%;
  }
}

@media (max-width: 640px) {
  .calendar-container {
    padding: 0;
    max-width: 100%;
    margin: 0;
  }

  .calendar-button {
    font-size: 10px;
    padding: 2px;
  }

  .calendar-day {
    padding: 2px;
    font-size: 12px;
  }

  .calendar-event {
    width: 100%;
    /* Span across the full width of the day box */
    white-space: nowrap;
    /* Prevent wrapping of event text */
    overflow: hidden;
    /* Hide any overflowed text */
    text-overflow: ellipsis;
    /* Add ellipsis if text is cut off */
    font-size: 10px;
    padding: 1px 2px;
    margin-bottom: 2px;
    background-color: #2563eb;
    /* Tailwind blue-600 */
    color: white;
    border-radius: 4px;
    display: inline-block;
  }

  .calendar-day:hover .calendar-event {
    background-color: #1e3a8a;
    /* Tailwind blue-800 */
  }
}

body {
  color: #171717;
  /* Changed from var(--foreground) */
  background: linear-gradient(180deg, #004aff, #0085ff);
  background-size: 100% 400%;
  animation: vertical-gradient 10s ease infinite;
  font-family: system-ui, -apple-system, sans-serif;
  margin: 0;
  min-height: 100vh;
}


* {
  transition: all 0.3s ease-in-out;
  box-sizing: border-box;
}

button,
a {
  position: relative;
  -webkit-tap-highlight-color: transparent;
}

/* Optional custom styles for Calendar */
.calendar-container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 8px;
}

.calendar-day {
  transition: background-color 0.3s ease-in-out;
  text-align: center;
  padding: 4px;
  border: 1px solid #e5e7eb;
  /* Tailwind gray-300 */
}

.calendar-day:hover {
  background-color: #f3f4f6;
  /* Tailwind gray-100 */
}

.calendar-event {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 2px 4px;
  font-size: 12px;
  margin: 2px 0;
  background-color: #2563eb;
  /* Tailwind blue-600 */
  color: white;
  border-radius: 4px;
  cursor: pointer;
  display: block;
}

.calendar-event:hover {
  background-color: #1e3a8a;
  /* Tailwind blue-800 */
}



.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.75);
  z-index: 1000;
}

.modal {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 1rem;
  border-radius: 0.5rem;
  max-width: 90%;
  max-height: 90vh;
  width: auto;
  overflow: auto;
  outline: none;
}

.modal img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}