'use client';

import React, { useState } from 'react';
import { useAuthState } from 'react-firebase-hooks/auth';
import { auth } from '../../lib/firebase/firebase';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import Image from 'next/image';

export default function InvoicePage() {
    const [user] = useAuthState(auth);
    const [showAutoFillModal, setShowAutoFillModal] = useState(false);
    const [autoFillText, setAutoFillText] = useState('');
    const [isProcessing, setIsProcessing] = useState(false);

    // Company info (pre-filled)
    const [companyInfo] = useState({
        name: 'Detail On The Go',
        contact: '<PERSON>',
        phone: '(*************',
        email: '<EMAIL>'
    });

    // Bill to information
    const [billTo, setBillTo] = useState({
        company: '',
        address: '',
        city: '',
        state: '',
        zip: '',
        country: 'USA'
    });

    // Get today's date in YYYY-MM-DD format
    const getTodayDate = () => {
        const today = new Date();
        return today.toISOString().split('T')[0];
    };

    // Invoice details
    const [invoiceDetails, setInvoiceDetails] = useState({
        serviceDate: getTodayDate(),
        invoiceDate: getTodayDate(),
        paymentTerms: 'Due Immediately'
    });

    // Services array - start with just one empty row
    const [services, setServices] = useState([
        { description: '', unitNumber: '', quantity: 1, rate: 0 }
    ]);

    // Add new service row
    const addService = () => {
        setServices([...services, { description: '', unitNumber: '', quantity: 1, rate: 0 }]);
    };

    // Remove service row
    const removeService = (index) => {
        setServices(services.filter((_, i) => i !== index));
    };

    // Update service
    const updateService = (index, field, value) => {
        const updatedServices = [...services];
        updatedServices[index][field] = value;
        setServices(updatedServices);
    };

    // Calculate totals
    const subtotal = services.reduce((sum, service) => sum + (service.quantity * service.rate), 0);
    const tax = 0; // No tax for this example
    const total = subtotal + tax;

    // Auto-fill function using AI
    const handleAutoFill = async () => {
        if (!autoFillText.trim()) return;

        setIsProcessing(true);
        try {
            const response = await fetch('/api/auto-fill-invoice', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ text: autoFillText }),
            });

            if (!response.ok) {
                throw new Error('Failed to process auto-fill');
            }

            const data = await response.json();

            // Update invoice fields with AI-parsed data
            if (data.billTo) {
                setBillTo(prev => ({ ...prev, ...data.billTo }));
            }
            if (data.invoiceDetails) {
                setInvoiceDetails(prev => ({ ...prev, ...data.invoiceDetails }));
            }
            if (data.services && data.services.length > 0) {
                setServices(data.services);
            }

            setShowAutoFillModal(false);
            setAutoFillText('');
        } catch (error) {
            console.error('Auto-fill error:', error);
            alert('Failed to auto-fill invoice. Please try again.');
        } finally {
            setIsProcessing(false);
        }
    };

    // Download PDF function
    const handleDownloadPDF = async () => {
        try {
            const element = document.getElementById('invoice-content');
            if (!element) return;

            // Hide elements that shouldn't appear in PDF
            const hideElements = element.querySelectorAll('.pdf-hide');
            hideElements.forEach(el => el.style.display = 'none');

            // Wait a bit for any layout changes
            await new Promise(resolve => setTimeout(resolve, 100));

            // Create canvas from the invoice content
            const canvas = await html2canvas(element, {
                scale: 3, // Even higher quality
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                logging: false,
                letterRendering: true,
                foreignObjectRendering: true
            });

            // Restore hidden elements
            hideElements.forEach(el => el.style.display = '');

            const imgData = canvas.toDataURL('image/png');

            // Create PDF
            const pdf = new jsPDF('p', 'mm', 'a4');
            const imgWidth = 210; // A4 width in mm
            const pageHeight = 295; // A4 height in mm
            const imgHeight = (canvas.height * imgWidth) / canvas.width;
            let heightLeft = imgHeight;

            let position = 0;

            // Add first page
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;

            // Add additional pages if needed
            while (heightLeft >= 0) {
                position = heightLeft - imgHeight;
                pdf.addPage();
                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;
            }

            // Generate filename with current date
            const today = new Date();
            const dateStr = today.toISOString().split('T')[0];
            const clientName = billTo.company || 'Client';
            const filename = `Invoice_${clientName.replace(/[^a-zA-Z0-9]/g, '_')}_${dateStr}.pdf`;

            // Download the PDF
            pdf.save(filename);
        } catch (error) {
            console.error('Error generating PDF:', error);
            alert('Failed to generate PDF. Please try again.');
        }
    };

    return (
        <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto">
                {/* Header with Buttons */}
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-3xl font-bold text-gray-800">Fleet Invoice Generator</h1>
                    <div className="flex space-x-3">
                        <button
                            onClick={() => setShowAutoFillModal(true)}
                            className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-semibold transition duration-200"
                        >
                            Auto Fill
                        </button>
                        <button
                            onClick={handleDownloadPDF}
                            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold transition duration-200"
                        >
                            Download PDF
                        </button>
                    </div>
                </div>

                {/* Invoice Container */}
                <div id="invoice-content" className="bg-white rounded-lg shadow-lg p-8">
                    {/* Invoice Header */}
                    <div className="text-center mb-8">
                        <h1 className="text-4xl font-bold text-gray-800 mb-2">INVOICE</h1>
                    </div>

                    {/* Company Info and Bill To Section */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                        {/* Company Info */}
                        <div>
                            {/* Logo on top of Blue Box */}
                            <div className="relative mb-4">
                                <div className="bg-blue-600 rounded-lg h-16"></div>
                                <div className="absolute inset-0 flex justify-center items-center">
                                    <Image
                                        src="/embroyder.png"
                                        alt="Detail On The Go Logo"
                                        width={250}
                                        height={80}
                                        className="object-contain"
                                    />
                                </div>
                            </div>
                            <h2 className="text-xl font-bold text-gray-800 mb-4">{companyInfo.name}</h2>
                            <div className="space-y-1 text-gray-600">
                                <p>{companyInfo.contact}</p>
                                <p>Phone: {companyInfo.phone}</p>
                                <p>Email: {companyInfo.email}</p>
                            </div>
                        </div>

                        {/* Bill To */}
                        <div>
                            <h3 className="text-lg font-semibold text-gray-800 mb-4">BILL TO:</h3>
                            <div className="space-y-2">
                                <input
                                    type="text"
                                    value={billTo.company}
                                    onChange={(e) => setBillTo({...billTo, company: e.target.value})}
                                    className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-500"
                                    placeholder="Company Name"
                                />
                                <input
                                    type="text"
                                    value={billTo.address}
                                    onChange={(e) => setBillTo({...billTo, address: e.target.value})}
                                    className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-500"
                                    placeholder="Address"
                                />
                                <div className="flex space-x-2">
                                    <input
                                        type="text"
                                        value={billTo.city}
                                        onChange={(e) => setBillTo({...billTo, city: e.target.value})}
                                        className="flex-1 p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-500 print:border-none print:p-0"
                                        placeholder="City"
                                    />
                                    <input
                                        type="text"
                                        value={billTo.state}
                                        onChange={(e) => setBillTo({...billTo, state: e.target.value})}
                                        className="w-20 p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-500 print:border-none print:p-0"
                                        placeholder="State"
                                    />
                                    <input
                                        type="text"
                                        value={billTo.zip}
                                        onChange={(e) => setBillTo({...billTo, zip: e.target.value})}
                                        className="w-24 p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-500 print:border-none print:p-0"
                                        placeholder="ZIP"
                                    />
                                </div>
                                <input
                                    type="text"
                                    value={billTo.country}
                                    onChange={(e) => setBillTo({...billTo, country: e.target.value})}
                                    className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-500 print:border-none print:p-0"
                                    placeholder="Country"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Invoice Details */}
                    <div className="mb-8">
                        <h3 className="text-lg font-semibold text-gray-800 mb-4">INVOICE DETAILS:</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Service Date:</label>
                                <input
                                    type="date"
                                    value={invoiceDetails.serviceDate}
                                    onChange={(e) => setInvoiceDetails({...invoiceDetails, serviceDate: e.target.value})}
                                    className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-500 print:border-none print:p-0"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Invoice Date:</label>
                                <input
                                    type="date"
                                    value={invoiceDetails.invoiceDate}
                                    onChange={(e) => setInvoiceDetails({...invoiceDetails, invoiceDate: e.target.value})}
                                    className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-500 print:border-none print:p-0"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Payment Terms:</label>
                                <input
                                    type="text"
                                    value={invoiceDetails.paymentTerms}
                                    onChange={(e) => setInvoiceDetails({...invoiceDetails, paymentTerms: e.target.value})}
                                    className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-500 print:border-none print:p-0"
                                    placeholder="Payment Terms"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Services Table */}
                    <div className="mb-8">
                        <h3 className="text-lg font-semibold text-gray-800 mb-4">SERVICES PROVIDED</h3>
                        <div className="overflow-x-auto">
                            <table className="w-full border-collapse border border-gray-300">
                                <thead>
                                    <tr className="bg-gray-50">
                                        <th className="border border-gray-300 px-4 py-2 text-left">Description</th>
                                        <th className="border border-gray-300 px-4 py-2 text-left">Unit Number</th>
                                        <th className="border border-gray-300 px-4 py-2 text-center">Quantity</th>
                                        <th className="border border-gray-300 px-4 py-2 text-right">Rate</th>
                                        <th className="border border-gray-300 px-4 py-2 text-right">Total</th>
                                        <th className="border border-gray-300 px-4 py-2 text-center pdf-hide">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {services.map((service, index) => (
                                        <tr key={index}>
                                            <td className="border border-gray-300 px-4 py-2">
                                                <input
                                                    type="text"
                                                    value={service.description}
                                                    onChange={(e) => updateService(index, 'description', e.target.value)}
                                                    className="w-full p-1 border-none focus:outline-none print:border-none"
                                                    placeholder="Service description"
                                                />
                                            </td>
                                            <td className="border border-gray-300 px-4 py-2">
                                                <input
                                                    type="text"
                                                    value={service.unitNumber}
                                                    onChange={(e) => updateService(index, 'unitNumber', e.target.value)}
                                                    className="w-full p-1 border-none focus:outline-none print:border-none"
                                                    placeholder="Unit #"
                                                />
                                            </td>
                                            <td className="border border-gray-300 px-4 py-2 text-center">
                                                <input
                                                    type="number"
                                                    value={service.quantity}
                                                    onChange={(e) => updateService(index, 'quantity', parseInt(e.target.value) || 0)}
                                                    className="w-full p-1 border-none focus:outline-none text-center print:border-none"
                                                    min="0"
                                                />
                                            </td>
                                            <td className="border border-gray-300 px-4 py-2 text-right">
                                                <input
                                                    type="number"
                                                    value={service.rate}
                                                    onChange={(e) => updateService(index, 'rate', parseFloat(e.target.value) || 0)}
                                                    className="w-full p-1 border-none focus:outline-none text-right print:border-none"
                                                    min="0"
                                                    step="0.01"
                                                />
                                            </td>
                                            <td className="border border-gray-300 px-4 py-2 text-right">
                                                ${(service.quantity * service.rate).toFixed(2)}
                                            </td>
                                            <td className="border border-gray-300 px-4 py-2 text-center pdf-hide">
                                                <button
                                                    onClick={() => removeService(index)}
                                                    className="text-red-600 hover:text-red-800 font-semibold"
                                                >
                                                    Remove
                                                </button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                        
                        <button
                            onClick={addService}
                            className="mt-4 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded font-semibold transition duration-200 pdf-hide"
                        >
                            Add Service
                        </button>
                    </div>

                    {/* Totals */}
                    <div className="flex justify-end mb-8">
                        <div className="w-64">
                            <div className="flex justify-between py-2 border-b border-gray-300">
                                <span className="font-semibold">Subtotal:</span>
                                <span>${subtotal.toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between py-2 border-b border-gray-300">
                                <span className="font-semibold">Tax:</span>
                                <span>${tax.toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between py-2 text-xl font-bold">
                                <span>TOTAL:</span>
                                <span>${total.toFixed(2)}</span>
                            </div>
                        </div>
                    </div>

                    {/* Footer */}
                    <div className="text-center border-t border-gray-300 pt-6">
                        <p className="text-lg font-semibold text-gray-800 mb-2">Thank you for your business!</p>
                        <div className="space-y-1 text-gray-600">
                            <p>Best,</p>
                            <p className="font-semibold">{companyInfo.contact}</p>
                            <p>Your Detailer</p>
                            <p>{companyInfo.phone}</p>
                            <p>detailongo.com</p>
                        </div>
                    </div>
                </div>

                {/* Auto Fill Modal */}
                {showAutoFillModal && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                        <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-y-auto">
                            <div className="p-6">
                                <div className="flex justify-between items-center mb-4">
                                    <h2 className="text-2xl font-bold text-gray-800">Auto Fill Invoice</h2>
                                    <button
                                        onClick={() => setShowAutoFillModal(false)}
                                        className="text-gray-500 hover:text-gray-700 text-2xl font-bold"
                                    >
                                        ×
                                    </button>
                                </div>

                                <div className="mb-4">
                                    <p className="text-gray-600 mb-3">
                                        Paste or type invoice information below. Include client details, service dates,
                                        vehicle numbers, services, and pricing. The AI will automatically parse and fill the invoice.
                                    </p>
                                    <p className="text-sm text-gray-500 mb-4">
                                        Example: "Invoice for American Red Cross, 2518 Ridge Ct, Lawrence KS 66046.
                                        Service date Aug 10, 2025. Exterior wash for vehicles 35031, 33794, 34365 at $69 each."
                                    </p>
                                </div>

                                <textarea
                                    value={autoFillText}
                                    onChange={(e) => setAutoFillText(e.target.value)}
                                    placeholder="Paste your invoice information here..."
                                    className="w-full h-40 p-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 resize-none"
                                    disabled={isProcessing}
                                />

                                <div className="flex justify-end space-x-3 mt-6">
                                    <button
                                        onClick={() => setShowAutoFillModal(false)}
                                        className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition duration-200"
                                        disabled={isProcessing}
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        onClick={handleAutoFill}
                                        disabled={!autoFillText.trim() || isProcessing}
                                        className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-semibold transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        {isProcessing ? 'Processing...' : 'Fill Invoice'}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}
