// app/layout.js
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>st_Mono } from "next/font/google";
import "./globals.css";
import Header from "../components/header";
import NetworkStatus from "../components/NetworkStatus"; // Create this new component

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const viewport = {
  themeColor: "#ffffff",
  colorScheme: "light",
};

export const metadata = {
  title: "Detail On The Go",
  description: "Manage your Detail On The Go.",
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "black-translucent",
    title: "Detail On The Go",
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        <meta name="msapplication-config" content="/browserconfig.xml" />
        <meta name="msapplication-TileColor" content="#ffffff" />
        <script
          src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCwkqP67w9Hse_VUD78dsGx_D3cSZ-0Gac&libraries=places"
          async
          defer
        ></script>
      </head>
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        {/* Client-side components */}
        <NetworkStatus />

        <Header />
        <div className="pt-16">
          {children}
        </div>

      </body>
    </html>
  );
}