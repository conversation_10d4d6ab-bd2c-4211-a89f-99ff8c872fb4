'use client';

import React, { useState } from 'react';
import CreatePaymentLinkPopup from '../../components/CreatePaymentLinkPopup';

// Add export default here
export default function TestStripePaymentLinkPage() {
    const [isPopupOpen, setIsPopupOpen] = useState(false);

    return (
        <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-xl mx-auto bg-white rounded-2xl shadow-xl p-8">
                <h1 className="text-3xl font-bold text-gray-800 mb-2">
                    Create a Payment Link
                </h1>
                <p className="text-gray-600 mb-6">
                    Generate a one-time payment link for a client.
                </p>
                <button
                    onClick={() => setIsPopupOpen(true)}
                    className="rounded-xl px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold transition duration-200 shadow-md"
                >
                    Create Payment Link
                </button>
            </div>

            {isPopupOpen && (
                <CreatePaymentLinkPopup onClose={() => setIsPopupOpen(false)} />
            )}
        </div>
    );
}