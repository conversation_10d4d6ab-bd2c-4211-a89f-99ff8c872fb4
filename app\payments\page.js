'use client';

import React from 'react';
import { useState, useEffect, useMemo } from 'react';
import { getUser, onAuthStateChangedListener } from '../../auth';
import { useTable, useSortBy, useFilters, useGlobalFilter, usePagination } from 'react-table';

const PaymentsPage = () => {
  const [payments, setPayments] = useState([]);
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  useEffect(() => {
    const fetchPayments = async (user) => {
      if (!user) return;

      try {
        const response = await fetch(
          `https://us-central1-detail-on-the-go-universal.cloudfunctions.net/show-payments3?location=${"stl"}`
        );
        if (!response.ok) {
          throw new Error(`Failed to fetch payments: ${response.statusText}`);
        }

        const data = await response.json();
        const paymentsWithNet = data.payments.map((payment) => {
          const gross = parseFloat(payment.grossAmount);
          const fee = (gross * 0.029) + 0.30;
          const net = gross - fee;
          return { ...payment, netAmountCalculated: net.toFixed(2), expanded: false };
        });
        setPayments(paymentsWithNet);
        setSuccessMessage('Payments loaded successfully.');
        setErrorMessage('');
      } catch (error) {
        console.error('Error fetching payments:', error);
        setErrorMessage(error.message);
        setSuccessMessage('');
      }
    };

    const unsubscribe = onAuthStateChangedListener((user) => {
      fetchPayments(user);
    });
    return () => unsubscribe();
  }, []);

  const columns = useMemo(
    () => [
      {
        Header: 'Expand',
        id: 'expander',
        Cell: ({ row }) => (
          <button onClick={() => toggleExpand(row.id)}>
            {row.original.expanded ? 'Collapse' : 'Expand'}
          </button>
        ),
      },
      { Header: 'Customer Name', accessor: 'customerName' },
      { Header: 'Email', accessor: 'email' },
      { Header: 'Date', accessor: 'transactionTime', Cell: ({ value }) => formatDate(value) },
      { Header: 'Gross Amount', accessor: 'grossAmount', Cell: ({ value }) => formatAmount(value) },
      { Header: 'Net Amount', accessor: 'netAmountCalculated', Cell: ({ value }) => `$${value}` },
      { Header: 'Location', accessor: 'location' },
      { Header: 'Payment Method', accessor: 'paymentMethod' },
      {
        Header: 'Customer Address',
        accessor: 'customerAddress',
        Cell: ({ value }) => {
          if (!value || typeof value !== 'object') return 'N/A';
          return `${value.line1 || ''} ${value.line2 || ''}, ${value.city || ''}, ${value.state || ''} ${value.postal_code || ''}, ${value.country || ''}`;
        },
      },
      { Header: 'Customer Phone', accessor: 'customerPhone' },
      { Header: 'Payment Collected', accessor: 'paymentCollected' },
      {
        Header: 'Stripe Fee',
        accessor: 'dummyFee',
        Cell: ({ value, row }) => {
          const gross = parseFloat(row.original.grossAmount);
          const fee = (gross * 0.029) + 0.30;
          return `$${fee.toFixed(2)}`;
        },
      },
      {
        Header: 'Description',
        accessor: 'description',
        Cell: ({ value }) => {
          if (!value) return '';
          const maxLength = 30;
          return value.length > maxLength ? `${value.substring(0, maxLength)}...` : value;
        },
      },
      { Header: 'Payment Status', accessor: 'status' },
      { Header: 'Timestamp', accessor: 'created', Cell: ({ value }) => formatDate(value * 1000) },
      { Header: 'Customer ID', accessor: 'customerID' },
      { Header: 'Payment Intent ID', accessor: 'paymentIntentID' },
    ],
    []
  );

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    page,
    prepareRow,
    setGlobalFilter,
    canPreviousPage,
    canNextPage,
    pageOptions,
    gotoPage,
    nextPage,
    previousPage,
    setPageSize,
    state: { globalFilter, pageIndex, pageSize },
  } = useTable(
    { columns, data: payments, initialState: { pageIndex: 0 } },
    useGlobalFilter,
    useFilters,
    useSortBy,
    usePagination
  );

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Invalid Date';
    return date.toLocaleString();
  };

  const formatAmount = (amount) => {
    if (amount === undefined || amount === null) return '$0.00';
    return `$${parseFloat(amount).toFixed(2)}`;
  };

  const toggleExpand = (rowId) => {
    setPayments((prevPayments) =>
      prevPayments.map((payment, index) =>
        index === parseInt(rowId) ? { ...payment, expanded: !payment.expanded } : payment
      )
    );
  };

  return (
    <div className="min-h-screen p-4 bg-gray-100">
      <h1 className="text-xl font-semibold text-gray-900 mb-4">Customer Payments</h1>
      {successMessage && <div className="text-green-600 mb-4">{successMessage}</div>}
      {errorMessage && <div className="text-red-600 mb-4">{errorMessage}</div>}

      <input
        type="text"
        value={globalFilter || ''}
        onChange={(e) => setGlobalFilter(e.target.value)}
        placeholder="Search payments..."
        className="w-full p-2 mb-4 border rounded"
      />

      <table {...getTableProps()} className="w-full border-collapse">
        <thead>
          {headerGroups.map((headerGroup) => (
            <tr key={headerGroup.getHeaderGroupProps().key} {...headerGroup.getHeaderGroupProps()}>
              {headerGroup.headers.map((column) => (
                <th key={column.getHeaderProps().key} {...column.getHeaderProps()} className="p-2 border">
                  {column.render('Header')}
                  <span>{column.isSorted ? (column.isSortedDesc ? ' 🔽' : ' 🔼') : ''}</span>
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody {...getTableBodyProps()}>
          {page.map((row) => {
            prepareRow(row);
            return (
              <React.Fragment key={row.getRowProps().key}>
                <tr key={row.getRowProps().key} {...row.getRowProps()} style={{ height: '30px' }}>
                  {row.cells.map((cell) => (
                    <td key={cell.getCellProps().key} {...cell.getCellProps()} className="p-2 border">
                      {cell.render('Cell')}
                    </td>
                  ))}
                </tr>
                {row.original.expanded && (
                  <tr>
                    <td colSpan={columns.length} className="p-2 border">
                      <pre>{JSON.stringify(row.original, null, 2)}</pre>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            );
          })}
        </tbody>
      </table>

      <div className="mt-4 flex justify-between items-center">
        <span>
          Page{' '}
          <strong>
            {pageIndex + 1} of {pageOptions.length}
          </strong>{' '}
        </span>
        <span>
          | Go to page:{' '}
          <input
            type="number"
            defaultValue={pageIndex + 1}
            onChange={(e) => {
              const page = e.target.value ? Number(e.target.value) - 1 : 0;
              gotoPage(page);
            }}
            className="w-16 border rounded"
          />
        </span>
        <select
          value={pageSize}
          onChange={(e) => setPageSize(Number(e.target.value))}
          className="border rounded"
        >
          {[10, 20, 30, 40, 50].map((pageSize) => (
            <option key={pageSize} value={pageSize}>
              Show {pageSize}
            </option>
          ))}
        </select>
        <div>
          <button onClick={() => gotoPage(0)} disabled={!canPreviousPage}>
            {'<<'}
          </button>
          <button onClick={() => previousPage()} disabled={!canPreviousPage}>
            {'<'}
          </button>
          <button onClick={() => nextPage()} disabled={!canNextPage}>
            {'>'}
          </button>
          <button onClick={() => gotoPage(pageOptions.length - 1)} disabled={!canNextPage}>
            {'>>'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default PaymentsPage;