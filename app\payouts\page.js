'use client';

import React from 'react';
import { useState, useEffect, useMemo } from 'react';
import { getUser, onAuthStateChangedListener } from '../../auth';
import { useTable, useSortBy, useFilters, useGlobalFilter, usePagination } from 'react-table';
import { db } from '../../lib/firebase/firebase';
import { collection, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy } from 'firebase/firestore';

const PayoutsPage = () => {
    const [payouts, setPayouts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [errorMessage, setErrorMessage] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [editingPayout, setEditingPayout] = useState(null);
    const [filterByStatus, setFilterByStatus] = useState('all');

    useEffect(() => {
        const fetchPayouts = async (user) => {
            if (!user) return;

            try {
                setLoading(true);
                setErrorMessage('');

                // Create a query for the payments collection
                const paymentsRef = collection(db, 'payments');
                let paymentsQuery = paymentsRef;

                // Apply filters if needed
                if (filterByStatus !== 'all') {
                    paymentsQuery = query(paymentsRef, where('payoutStatus', '==', filterByStatus));
                }

                // Order by timestamp descending
                paymentsQuery = query(paymentsQuery, orderBy('timestamp', 'desc'));

                const snapshot = await getDocs(paymentsQuery);
                const payoutsData = snapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data(),
                    // Format timestamp to string for display
                    formattedTimestamp: doc.data().timestamp ?
                        new Date(doc.data().timestamp.toDate()).toLocaleString() : 'N/A',
                    // Format event start time if it exists
                    formattedEventStartTime: doc.data().eventStartTime ?
                        new Date(doc.data().eventStartTime.toDate()).toLocaleString() : 'N/A',
                    expanded: false
                }));

                setPayouts(payoutsData);
                setSuccessMessage('Payouts loaded successfully.');
            } catch (error) {
                console.error('Error fetching payouts:', error);
                setErrorMessage(`Failed to load payouts: ${error.message}`);
            } finally {
                setLoading(false);
            }
        };

        const unsubscribe = onAuthStateChangedListener((user) => {
            fetchPayouts(user);
        });

        return () => unsubscribe();
    }, [filterByStatus]);

    const updatePayoutStatus = async (id, newStatus) => {
        try {
            setLoading(true);
            const payoutRef = doc(db, 'payments', id);
            await updateDoc(payoutRef, { payoutStatus: newStatus });

            // Update local state
            setPayouts(prevPayouts =>
                prevPayouts.map(payout =>
                    payout.id === id ? { ...payout, payoutStatus: newStatus } : payout
                )
            );

            setSuccessMessage(`Payout status updated to ${newStatus}`);
        } catch (error) {
            console.error('Error updating payout status:', error);
            setErrorMessage(`Failed to update status: ${error.message}`);
        } finally {
            setLoading(false);
        }
    };

    const savePayoutChanges = async () => {
        if (!editingPayout) return;

        try {
            setLoading(true);
            const payoutRef = doc(db, 'payments', editingPayout.id);

            // Extract the fields to update (excluding id, expanded, etc.)
            const { id, expanded, formattedTimestamp, formattedEventStartTime, ...dataToUpdate } = editingPayout;

            await updateDoc(payoutRef, dataToUpdate);

            // Update local state
            setPayouts(prevPayouts =>
                prevPayouts.map(payout =>
                    payout.id === id ? { ...editingPayout, formattedTimestamp, formattedEventStartTime } : payout
                )
            );

            setSuccessMessage('Payout updated successfully');
            setEditingPayout(null); // Close edit form
        } catch (error) {
            console.error('Error updating payout:', error);
            setErrorMessage(`Failed to update payout: ${error.message}`);
        } finally {
            setLoading(false);
        }
    };

    const deletePayout = async (id) => {
        if (!window.confirm('Are you sure you want to delete this payout record? This action cannot be undone.')) {
            return;
        }

        try {
            setLoading(true);
            const payoutRef = doc(db, 'payments', id);
            await deleteDoc(payoutRef);

            // Update local state
            setPayouts(prevPayouts => prevPayouts.filter(payout => payout.id !== id));

            setSuccessMessage('Payout deleted successfully');
        } catch (error) {
            console.error('Error deleting payout:', error);
            setErrorMessage(`Failed to delete payout: ${error.message}`);
        } finally {
            setLoading(false);
        }
    };

    const toggleExpand = (rowId) => {
        setPayouts(prevPayouts =>
            prevPayouts.map((payout, index) =>
                index === parseInt(rowId) ? { ...payout, expanded: !payout.expanded } : payout
            )
        );
    };

    const handleEditClick = (payout) => {
        setEditingPayout({ ...payout });
    };

    const handleCancelEdit = () => {
        setEditingPayout(null);
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setEditingPayout(prev => ({ ...prev, [name]: value }));
    };

    const columns = useMemo(
        () => [
            {
                Header: 'Actions',
                id: 'actions',
                Cell: ({ row }) => (
                    <div className="flex space-x-2">
                        <button
                            onClick={() => toggleExpand(row.id)}
                            className="px-2 py-1 bg-gray-200 rounded hover:bg-gray-300"
                        >
                            {row.original.expanded ? 'Collapse' : 'Expand'}
                        </button>
                        <button
                            onClick={() => handleEditClick(row.original)}
                            className="px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
                        >
                            Edit
                        </button>
                    </div>
                ),
            },
            {
                Header: 'Customer',
                accessor: 'customerName',
                Cell: ({ value }) => value || 'N/A'
            },
            {
                Header: 'Email',
                accessor: 'customerEmail',
                Cell: ({ value }) => value || 'N/A'
            },
            {
                Header: 'Date',
                accessor: 'formattedTimestamp'
            },
            {
                Header: 'Event Date',
                accessor: 'formattedEventStartTime'
            },
            {
                Header: 'Amount',
                accessor: 'amount',
                Cell: ({ value }) => `$${parseFloat(value).toFixed(2)}`
            },
            {
                Header: 'Amount After Fees',
                accessor: 'amountAfterFees',
                Cell: ({ value }) => `$${parseFloat(value).toFixed(2)}`
            },
            {
                Header: 'Stripe Fees',
                accessor: 'stripeFees',
                Cell: ({ value }) => value ? `$${parseFloat(value).toFixed(2)}` : 'N/A'
            },
            {
                Header: 'Location',
                accessor: 'location',
                Cell: ({ value }) => value || 'N/A'
            },
            {
                Header: 'Description',
                accessor: 'description',
                Cell: ({ value }) => {
                    if (!value) return 'N/A';
                    const maxLength = 30;
                    return value.length > maxLength ? `${value.substring(0, maxLength)}...` : value;
                }
            },
            {
                Header: 'Payout Status',
                accessor: 'payoutStatus',
                Cell: ({ value, row }) => (
                    <div className="flex flex-col space-y-1">
                        <span className={`font-semibold ${value === 'PAID' ? 'text-green-600' :
                                value === 'UNPAID' ? 'text-red-600' : 'text-yellow-600'
                            }`}>
                            {value || 'N/A'}
                        </span>
                        <div className="flex space-x-1">
                            <button
                                onClick={() => updatePayoutStatus(row.original.id, 'PAID')}
                                className={`px-2 py-1 text-xs rounded ${value === 'PAID' ? 'bg-gray-200' : 'bg-green-500 text-white hover:bg-green-600'
                                    }`}
                                disabled={value === 'PAID'}
                            >
                                Mark Paid
                            </button>
                            <button
                                onClick={() => updatePayoutStatus(row.original.id, 'UNPAID')}
                                className={`px-2 py-1 text-xs rounded ${value === 'UNPAID' ? 'bg-gray-200' : 'bg-red-500 text-white hover:bg-red-600'
                                    }`}
                                disabled={value === 'UNPAID'}
                            >
                                Mark Unpaid
                            </button>
                        </div>
                    </div>
                )
            },
            {
                Header: 'Payment Method',
                accessor: 'paymentMethod',
                Cell: ({ value }) => value || 'N/A'
            },
            {
                Header: 'Stripe ID',
                accessor: 'stripePaymentIntentId',
                Cell: ({ value }) => {
                    if (!value) return 'N/A';
                    const shortValue = value.substring(0, 10) + '...';
                    return (
                        <span title={value}>
                            {shortValue}
                        </span>
                    );
                }
            },
        ],
        []
    );

    const {
        getTableProps,
        getTableBodyProps,
        headerGroups,
        page,
        prepareRow,
        setGlobalFilter,
        canPreviousPage,
        canNextPage,
        pageOptions,
        gotoPage,
        nextPage,
        previousPage,
        setPageSize,
        state: { globalFilter, pageIndex, pageSize },
    } = useTable(
        {
            columns,
            data: payouts,
            initialState: { pageIndex: 0, pageSize: 10 }
        },
        useGlobalFilter,
        useFilters,
        useSortBy,
        usePagination
    );

    return (
        <div className="min-h-screen p-4 bg-gray-100">
            <h1 className="text-xl font-semibold text-gray-900 mb-4">Payment Payouts Management</h1>

            {/* Status messages */}
            {successMessage && (
                <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 flex justify-between">
                    <span>{successMessage}</span>
                    <button onClick={() => setSuccessMessage('')} className="font-bold">×</button>
                </div>
            )}

            {errorMessage && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 flex justify-between">
                    <span>{errorMessage}</span>
                    <button onClick={() => setErrorMessage('')} className="font-bold">×</button>
                </div>
            )}

            {/* Filter & Search controls */}
            <div className="flex flex-col md:flex-row gap-4 mb-4">
                <div className="w-full md:w-1/3">
                    <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="status-filter">
                        Filter by Status
                    </label>
                    <select
                        id="status-filter"
                        value={filterByStatus}
                        onChange={(e) => setFilterByStatus(e.target.value)}
                        className="w-full p-2 border rounded"
                    >
                        <option value="all">All Statuses</option>
                        <option value="PAID">Paid</option>
                        <option value="UNPAID">Unpaid</option>
                        <option value="PENDING">Pending</option>
                    </select>
                </div>

                <div className="w-full md:w-2/3">
                    <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="search">
                        Search Payouts
                    </label>
                    <input
                        id="search"
                        type="text"
                        value={globalFilter || ''}
                        onChange={(e) => setGlobalFilter(e.target.value)}
                        placeholder="Search by name, email, location..."
                        className="w-full p-2 border rounded"
                    />
                </div>
            </div>

            {/* Edit Payout Modal */}
            {editingPayout && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white p-6 rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                        <h2 className="text-xl font-bold mb-4">Edit Payout</h2>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label className="block text-gray-700 text-sm font-bold mb-2">
                                    Customer Name
                                </label>
                                <input
                                    type="text"
                                    name="customerName"
                                    value={editingPayout.customerName || ''}
                                    onChange={handleInputChange}
                                    className="w-full p-2 border rounded"
                                />
                            </div>

                            <div>
                                <label className="block text-gray-700 text-sm font-bold mb-2">
                                    Customer Email
                                </label>
                                <input
                                    type="email"
                                    name="customerEmail"
                                    value={editingPayout.customerEmail || ''}
                                    onChange={handleInputChange}
                                    className="w-full p-2 border rounded"
                                />
                            </div>

                            <div>
                                <label className="block text-gray-700 text-sm font-bold mb-2">
                                    Amount ($)
                                </label>
                                <input
                                    type="number"
                                    name="amount"
                                    value={editingPayout.amount || 0}
                                    onChange={handleInputChange}
                                    className="w-full p-2 border rounded"
                                    step="0.01"
                                />
                            </div>

                            <div>
                                <label className="block text-gray-700 text-sm font-bold mb-2">
                                    Amount After Fees ($)
                                </label>
                                <input
                                    type="number"
                                    name="amountAfterFees"
                                    value={editingPayout.amountAfterFees || 0}
                                    onChange={handleInputChange}
                                    className="w-full p-2 border rounded"
                                    step="0.01"
                                />
                            </div>

                            <div>
                                <label className="block text-gray-700 text-sm font-bold mb-2">
                                    Location
                                </label>
                                <input
                                    type="text"
                                    name="location"
                                    value={editingPayout.location || ''}
                                    onChange={handleInputChange}
                                    className="w-full p-2 border rounded"
                                />
                            </div>

                            <div>
                                <label className="block text-gray-700 text-sm font-bold mb-2">
                                    Payout Status
                                </label>
                                <select
                                    name="payoutStatus"
                                    value={editingPayout.payoutStatus || 'UNPAID'}
                                    onChange={handleInputChange}
                                    className="w-full p-2 border rounded"
                                >
                                    <option value="PAID">PAID</option>
                                    <option value="UNPAID">UNPAID</option>
                                    <option value="PENDING">PENDING</option>
                                </select>
                            </div>

                            <div className="col-span-1 md:col-span-2">
                                <label className="block text-gray-700 text-sm font-bold mb-2">
                                    Description
                                </label>
                                <textarea
                                    name="description"
                                    value={editingPayout.description || ''}
                                    onChange={handleInputChange}
                                    className="w-full p-2 border rounded"
                                    rows="3"
                                ></textarea>
                            </div>

                            <div className="col-span-1 md:col-span-2">
                                <label className="block text-gray-700 text-sm font-bold mb-2">
                                    Address
                                </label>
                                <textarea
                                    name="address"
                                    value={editingPayout.address || ''}
                                    onChange={handleInputChange}
                                    className="w-full p-2 border rounded"
                                    rows="2"
                                ></textarea>
                            </div>
                        </div>

                        <div className="flex justify-end space-x-2">
                            <button
                                onClick={handleCancelEdit}
                                className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={savePayoutChanges}
                                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                                disabled={loading}
                            >
                                {loading ? 'Saving...' : 'Save Changes'}
                            </button>
                            <button
                                onClick={() => deletePayout(editingPayout.id)}
                                className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                                disabled={loading}
                            >
                                {loading ? 'Deleting...' : 'Delete Payout'}
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Table */}
            {loading && payouts.length === 0 ? (
                <div className="flex justify-center items-center h-64">
                    <div className="text-gray-500">Loading payouts...</div>
                </div>
            ) : payouts.length === 0 ? (
                <div className="flex justify-center items-center h-64 bg-white rounded-lg shadow">
                    <div className="text-gray-500">No payouts found matching your criteria.</div>
                </div>
            ) : (
                <div className="overflow-x-auto bg-white rounded-lg shadow">
                    <table {...getTableProps()} className="w-full border-collapse">
                        <thead>
                            {headerGroups.map(headerGroup => {
                                const { key, ...headerGroupProps } = headerGroup.getHeaderGroupProps();
                                return (
                                    <tr key={key} {...headerGroupProps}>
                                        {headerGroup.headers.map(column => {
                                            const { key, ...cellProps } = column.getHeaderProps(column.getSortByToggleProps());
                                            return (
                                                <th
                                                    key={key}
                                                    {...cellProps}
                                                    className="p-3 text-left bg-gray-50 text-gray-700 border-b font-medium text-sm"
                                                >
                                                    {column.render('Header')}
                                                    <span>
                                                        {column.isSorted
                                                            ? column.isSortedDesc
                                                                ? ' 🔽'
                                                                : ' 🔼'
                                                            : ''}
                                                    </span>
                                                </th>
                                            );
                                        })}
                                    </tr>
                                );
                            })}
                        </thead>
                        <tbody {...getTableBodyProps()}>
                            {page.map((row, i) => {
                                prepareRow(row);
                                const { key, ...rowProps } = row.getRowProps();
                                return (
                                    <React.Fragment key={row.id}>
                                        <tr
                                            key={key}
                                            {...rowProps}
                                            className={`border-b hover:bg-gray-50 ${i % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                                                }`}
                                        >
                                            {row.cells.map(cell => {
                                                const { key, ...cellProps } = cell.getCellProps();
                                                return (
                                                    <td
                                                        key={key}
                                                        {...cellProps}
                                                        className="p-3 text-sm text-gray-700"
                                                    >
                                                        {cell.render('Cell')}
                                                    </td>
                                                );
                                            })}
                                        </tr>
                                        {row.original.expanded && (
                                            <tr key={`${row.id}-expanded`} className="bg-gray-100">
                                                <td colSpan={columns.length} className="p-3">
                                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                                        {Object.entries(row.original)
                                                            .filter(([key]) => !['expanded', 'id'].includes(key))
                                                            .map(([key, value]) => (
                                                                <div key={key} className="overflow-hidden">
                                                                    <span className="font-semibold">{key}: </span>
                                                                    <span className="text-gray-700 break-all">
                                                                        {typeof value === 'object'
                                                                            ? JSON.stringify(value)
                                                                            : String(value)}
                                                                    </span>
                                                                </div>
                                                            ))}
                                                    </div>
                                                </td>
                                            </tr>
                                        )}
                                    </React.Fragment>
                                );
                            })}
                        </tbody>
                    </table>
                </div>
            )}

            {/* Pagination Controls */}
            <div className="mt-4 flex flex-col md:flex-row justify-between items-center">
                <div className="mb-2 md:mb-0">
                    <span className="text-sm text-gray-700">
                        Page{' '}
                        <span className="font-medium">{pageIndex + 1}</span> of{' '}
                        <span className="font-medium">{pageOptions.length}</span>
                    </span>
                </div>

                <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-700 mr-2">
                        Go to page:
                    </span>
                    <input
                        type="number"
                        min={1}
                        max={pageOptions.length}
                        defaultValue={pageIndex + 1}
                        onChange={e => {
                            const page = e.target.value ? Number(e.target.value) - 1 : 0;
                            gotoPage(page);
                        }}
                        className="w-16 p-1 border rounded text-sm"
                    />
                </div>

                <div className="flex items-center space-x-2">
                    <select
                        value={pageSize}
                        onChange={e => {
                            setPageSize(Number(e.target.value));
                        }}
                        className="p-1 border rounded text-sm"
                    >
                        {[10, 20, 30, 50, 100].map(pageSize => (
                            <option key={pageSize} value={pageSize}>
                                Show {pageSize}
                            </option>
                        ))}
                    </select>

                    <button
                        onClick={() => gotoPage(0)}
                        disabled={!canPreviousPage}
                        className={`px-2 py-1 rounded text-sm ${canPreviousPage
                                ? 'bg-blue-500 text-white hover:bg-blue-600'
                                : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                            }`}
                    >
                        {'<<'}
                    </button>
                    <button
                        onClick={() => previousPage()}
                        disabled={!canPreviousPage}
                        className={`px-2 py-1 rounded text-sm ${canPreviousPage
                                ? 'bg-blue-500 text-white hover:bg-blue-600'
                                : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                            }`}
                    >
                        {'<'}
                    </button>
                    <button
                        onClick={() => nextPage()}
                        disabled={!canNextPage}
                        className={`px-2 py-1 rounded text-sm ${canNextPage
                                ? 'bg-blue-500 text-white hover:bg-blue-600'
                                : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                            }`}
                    >
                        {'>'}
                    </button>
                    <button
                        onClick={() => gotoPage(pageOptions.length - 1)}
                        disabled={!canNextPage}
                        className={`px-2 py-1 rounded text-sm ${canNextPage
                                ? 'bg-blue-500 text-white hover:bg-blue-600'
                                : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                            }`}
                    >
                        {'>>'}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default PayoutsPage;