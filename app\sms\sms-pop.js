'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import PropTypes from 'prop-types';
import { onAuthStateChangedListener } from '../../auth.js'; // Adjust path as needed
import { db } from '../../lib/firebase/firebase'; // Adjust path as needed
import { doc, getDoc, setDoc, updateDoc, collection, onSnapshot } from 'firebase/firestore';
import PricingPopup from '../pricing/pricing-popup';

export default function MessengerPopup({ isOpen, onClose, businessNumber, clientNumber, location }) {
  const [clientInfo, setClientInfo] = useState({
    firstName: '',
    lastName: '',
    phone: ''
  });
  const [isEditingName, setIsEditingName] = useState(false);
  const [tempFirstName, setTempFirstName] = useState('');
  const [tempLastName, setTempLastName] = useState('');
  const [showPricingPopup, setShowPricingPopup] = useState(false);

  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(false);
  const [delay, setDelay] = useState(0);
  const [userFirstName, setUserFirstName] = useState('');
  const [userFullName, setUserFullName] = useState('');
  const [isGeneratingResponse, setIsGeneratingResponse] = useState(false);
  const [currentPhraseIndex, setCurrentPhraseIndex] = useState(0);
  const [selectedImage, setSelectedImage] = useState(null);
  const phrases = [
    "Formulating a response...",
    "Getting information",
    "Reading conversation",
    "Analyzing context",
    "Crafting reply"
  ];
  const messagesEndRef = useRef(null);

  // Handle ESC key for image modal
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === 'Escape' && selectedImage) {
        setSelectedImage(null);
      }
    };

    if (selectedImage) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [selectedImage]);

  const MAX_ROWS = 6;
  const ROW_HEIGHT = 24;             // adjust to match your line-height
  const MAX_HEIGHT = MAX_ROWS * ROW_HEIGHT;
  const textareaRef = useRef(null);


  useEffect(() => {
    let unsub;

    if (isOpen) {
      const collectionName = `sms-${location.toLowerCase()}`;
      const clientDocRef = doc(db, collectionName, clientNumber);

      // Set up real-time listener
      unsub = onSnapshot(clientDocRef, (doc) => {
        if (doc.exists()) {
          const clientData = doc.data();
          const rawMessages = clientData.messages || [];

          const processedMessages = rawMessages.map((msg) => {
            const ts = msg.timestamp;
            const date = new Date(
              (ts?.seconds || ts?._seconds || 0) * 1000 +
              (ts?.nanoseconds || ts?._nanoseconds || 0) / 1e6
            );

            return {
              ...msg,
              direction: msg.direction || 'incoming',
              timestamp: date.toLocaleString('en-US', {
                month: 'short',
                day: 'numeric',
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
              }),
              rawTimestamp: date.getTime(),
              message: msg.message || '',
            };
          }).sort((a, b) => a.rawTimestamp - b.rawTimestamp);

          setMessages(processedMessages);
          setClientInfo({
            firstName: clientData.firstName?.trim() || '',
            lastName: clientData.lastName?.trim() || '',
            phone: clientNumber
          });
        }
      });
    }

    return () => {
      if (unsub) unsub();
    };
  }, [isOpen, businessNumber, clientNumber, location]); // Add proper dependencies

  // Remove the old setupRealtimeUpdates and fetchConversation functions
  // Replace with this simplified version
  const fetchInitialData = useCallback(async () => {
    if (!isOpen) return;

    try {
      const collectionName = `sms-${location.toLowerCase()}`;
      const clientDocRef = doc(db, collectionName, clientNumber);
      const clientSnap = await getDoc(clientDocRef);

      if (clientSnap.exists()) {
        const clientData = clientSnap.data();
        setClientInfo({
          firstName: clientData.firstName?.trim() || '',
          lastName: clientData.lastName?.trim() || '',
          phone: clientNumber
        });
      }
    } catch (error) {
      console.error('Error fetching initial data:', error);
    }
  }, [isOpen, businessNumber, clientNumber, location]);

  // Update the useEffect that triggers on open
  useEffect(() => {
    if (isOpen) {
      fetchInitialData();
    }
  }, [isOpen, fetchInitialData]);



  useEffect(() => {
    const el = textareaRef.current;
    if (!el) return;
    el.style.height = 'auto';
    const newH = Math.min(el.scrollHeight, MAX_HEIGHT);
    el.style.height = `${newH}px`;
    el.style.overflowY = el.scrollHeight > MAX_HEIGHT ? 'auto' : 'hidden';
  }, [newMessage]);
  const formatPhoneNumber = (phone) => {
    if (!phone) return '';
    const cleaned = phone.replace(/\D/g, '');

    // Format as (XXX) XXX-XXXX for US numbers
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    // Format as +X (XXX) XXX-XXXX for international numbers starting with +1
    if (cleaned.length === 11 && cleaned.startsWith('1')) {
      return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
    }
    // For other formats, just return the cleaned number
    return phone;
  };

  // Fetch authenticated user's name
  useEffect(() => {
    const unsubscribe = onAuthStateChangedListener((user) => {
      if (user?.displayName) {
        const [firstName] = user.displayName.split(' ');
        setUserFirstName(firstName);
        setUserFullName(user.displayName);
      } else {
        setUserFirstName('');
        setUserFullName('');
      }
    });
    return () => unsubscribe();
  }, []);

  const generateAutoResponse = useCallback(async () => {
    setIsGeneratingResponse(true);
    setIsLoading(true);
    try {
      const requestData = {
        businessNumber,
        clientNumber,
        messages: messages.slice(-10),
        userName: userFirstName
      };
      if (newMessage.trim()) {
        requestData.customPrompt = newMessage.trim();
      }

      const response = await fetch('/api/generate-response', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        throw new Error(`Failed to generate response: ${response.status}`);
      }

      const data = await response.json();
      console.log('🏷  [generateAutoResponse] raw API payload:', data);
      setNewMessage(data.response);
    } catch (error) {
      console.error('Error generating auto response:', error);
      alert('Failed to generate auto response. Please try again.');
    } finally {
      setIsLoading(false);
      setIsGeneratingResponse(false);
    }
  }, [businessNumber, clientNumber, messages, userFirstName, newMessage]);












  // Fetch conversation history
  const fetchConversation = useCallback(async () => {
    if (!businessNumber || !clientNumber || !location) return;

    try {
      const collectionName = `sms-${location.toLowerCase()}`;
      const clientDocRef = doc(db, collectionName, clientNumber);
      const clientSnap = await getDoc(clientDocRef);

      if (clientSnap.exists()) {
        const clientData = clientSnap.data();
        setClientInfo({
          firstName: clientData.firstName?.trim() || '',
          lastName: clientData.lastName?.trim() || '',
          phone: clientNumber
        });
      }
    } catch (error) {
      console.error('Error fetching initial conversation:', error);
    }
  }, [businessNumber, clientNumber, location]);



  const handleNameUpdate = async () => {
    try {
      if (!tempFirstName.trim() && !tempLastName.trim()) {
        alert('Please enter at least one name field');
        return;
      }

      const collectionName = `sms-${location.toLowerCase()}`;
      const clientDocRef = doc(db, collectionName, clientNumber);

      // Use setDoc with merge: true to update only specific fields
      await setDoc(clientDocRef, {
        firstName: tempFirstName.trim(),
        lastName: tempLastName.trim()
      }, { merge: true }); // This preserves existing data in the document

      setClientInfo(prev => ({
        ...prev,
        firstName: tempFirstName.trim(),
        lastName: tempLastName.trim()
      }));
      setIsEditingName(false);

    } catch (error) {
      console.error('Error updating name:', error);
      alert('Failed to update client name');
    }
  };




  useEffect(() => {
    if (isOpen) fetchConversation();
  }, [isOpen, fetchConversation]);

  // Scroll to latest message
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Cycle through phrases for overlay
  useEffect(() => {
    if (isGeneratingResponse) {
      const interval = setInterval(() => {
        setCurrentPhraseIndex((prev) => (prev + 1) % phrases.length);
      }, 2000); // Change every 2 seconds
      return () => clearInterval(interval);
    }
  }, [isGeneratingResponse, phrases.length]);

  // Fetch availability
  const fetchAvailability = useCallback(async () => {
    try {
      const response = await fetch(
        `https://us-central1-detail-on-the-go-universal.cloudfunctions.net/phone-availability?to=${encodeURIComponent(businessNumber)}&from=${encodeURIComponent(clientNumber)}`
      );
      if (!response.ok) throw new Error('Failed to fetch availability');
      return await response.json();
    } catch (error) {
      console.error('Error fetching availability:', error);
      return [];
    }
  }, [businessNumber, clientNumber]);

  // Send message
  const handleSendMessage = useCallback(async () => {
    if (!newMessage.trim()) return;
    setIsLoading(true);

    const signature = userFirstName ? `-${userFirstName}, Detail On The Go` : '-Detail On The Go';
    const messageWithSignature = `${newMessage.trim()} ${signature}`;

    try {
      // 1. Send the message via cloud function
      const smsResponse = await fetch(
        `https://us-central1-detail-on-the-go-universal.cloudfunctions.net/sms?to=${encodeURIComponent(clientNumber)}&from=${encodeURIComponent(businessNumber)}&message=${encodeURIComponent(messageWithSignature)}&delay=${delay}`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
        }
      );

      if (!smsResponse.ok) throw new Error('Failed to send message');

      // 2. Clear input field
      setNewMessage('');

      // 3. Send to Google Sheets if needed
      const now = new Date();
      const clientData = {
        timestamp: now.toLocaleString(),
        branch: location,
        businessNumber,
        name: `${clientInfo.firstName} ${clientInfo.lastName}`.trim() || 'Unknown Client',
        phone: clientNumber,
        message: messageWithSignature,
      };

      if (/(I've just arrived|I'm headed your|wrapping|finished)/i.test(newMessage)) {
        const sheetsResponse = await fetch(
          'https://us-central1-detail-on-the-go-universal.cloudfunctions.net/detail-sms-timing',
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(clientData),
          }
        );
        if (!sheetsResponse.ok) console.error('Failed to send client data to backend');
        else console.log('Client data successfully sent to backend');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      // Optionally restore message if send failed
      // setNewMessage(messageWithSignature);
    } finally {
      setIsLoading(false);
    }
  }, [newMessage, userFirstName, businessNumber, clientNumber, delay, location, clientInfo]);

  // Initiate call
  const initiateCall = useCallback(() => {
    fetch(
      `https://us-central1-detail-on-the-go-universal.cloudfunctions.net/LWR-create-call?businessnumber=${encodeURIComponent(businessNumber)}&forwardnumber=${encodeURIComponent(clientNumber)}&clientnumber=${encodeURIComponent(clientNumber)}`,
      { method: 'POST' }
    )
      .then((res) => {
        if (!res.ok) throw new Error('Failed to initiate call');
        return res.json();
      })
      .then(() => alert('Call initiated successfully!'))
      .catch((error) => {
        console.error('Error initiating call:', error);
        alert('Failed to initiate call.');
      });
  }, [businessNumber, clientNumber]);

  // Apply shortcut messages
  const applyShortcut = useCallback(async (shortcut) => {
    const messagesMap = {
      onMyWay: "I'm headed your way now.🚗",
      arrived: "I've just arrived, and will start shortly.🚗🚐",
      delay: "My current detail is taking a bit longer than expected, so I may be running late to our appointment today. I apologize for the delay and will update you as soon as I'm on my way.",
      early: "I've just finished my current detail earlier than expected, and can head your way now if that works with your schedule. Let me know if not - thanks!",
      finished1: "I'm close to wrapping up your detail now! If it's convenient, would you like to come look?",
      finished2: "I've just finished your detail. Thank you very much for having me out today, I'll see you next time!😊",
      LWRReview: "Thank you so much for having me out! If you enjoyed your detail experience, here's an easy link to leave a review😊 https://g.page/r/CS98X9jMS0IREBM/review",
    };
    if (shortcut === 'getAvailability') {
      setIsLoading(true);
      const availableSlots = await fetchAvailability();
      setIsLoading(false);
      setNewMessage(
        availableSlots.length > 0
          ? `Our next availability is:\n${availableSlots.slice(0, 5).join('\n')}. Do any of these times work with your schedule?`
          : 'I don’t have any availability in the next 60 days.'
      );
    } else {
      setNewMessage(messagesMap[shortcut] || '');
    }
  }, [fetchAvailability]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div
        className="bg-white rounded-lg shadow-lg w-full max-w-md md:max-h-[90vh] h-full md:h-auto flex flex-col overflow-hidden"
        style={{ maxHeight: '90vh' }}
      >
        {isGeneratingResponse && (
          <div className="absolute inset-0 z-10 flex items-center justify-center bg-gradient-to-r from-orange-400 via-orange-500 to-orange-600 animate-gradient-x">
            <p className="text-white text-lg font-semibold">{phrases[currentPhraseIndex]}</p>
          </div>
        )}
        {isFetching ? (
          <div className="flex justify-center items-center h-full p-4">
            <p className="text-gray-600">Loading conversation...</p>
          </div>
        ) : (
          <>
            <header className="flex justify-between items-center p-3 border-b"> {/* Reduced padding */}
              <div className="flex items-center gap-3"> {/* Reduced gap */}
                <div className="flex flex-col gap-0.5"> {/* Tighter vertical spacing */}
                  {isEditingName ? (
                    <div className="flex gap-2">
                      <input
                        value={tempFirstName}
                        onChange={(e) => setTempFirstName(e.target.value)}
                        placeholder="First name"
                        className="w-24 px-2 py-1 border rounded"
                      />
                      <input
                        value={tempLastName}
                        onChange={(e) => setTempLastName(e.target.value)}
                        placeholder="Last name"
                        className="w-24 px-2 py-1 border rounded"
                      />
                    </div>
                  ) : (
                    <h2 className="text-lg font-semibold leading-tight"> {/* Tighter line height */}
                      {clientInfo.firstName} {clientInfo.lastName}
                    </h2>
                  )}
                  <p className="text-sm text-gray-600">
                    {formatPhoneNumber(clientInfo.phone)}
                  </p>
                </div>
                <div className="flex gap-1">
                  {isEditingName ? (
                    <>
                      {/* Checkmark (Save) button */}
                      <button
                        onClick={handleNameUpdate}
                        className="p-1.5 text-white bg-green-600 rounded-md hover:bg-green-700"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </button>
                      {/* Cancel (X) button */}
                      <button
                        onClick={() => setIsEditingName(false)}
                        className="p-1.5 text-white bg-red-600 rounded-md hover:bg-red-700"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </>
                  ) : (
                    // Original Edit button
                    <button
                      onClick={() => {
                        setTempFirstName(clientInfo.firstName);
                        setTempLastName(clientInfo.lastName);
                        setIsEditingName(true);
                      }}
                      className="p-1.5 text-white bg-gray-600 rounded-md hover:bg-gray-700"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                      </svg>
                    </button>
                  )}

                  {/* Call button */}
                  <button
                    onClick={initiateCall}
                    className="p-1.5 text-white bg-green-600 rounded-md hover:bg-green-700"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                    </svg>
                  </button>

                  {/* Pricing Calculator button */}
                  <button
                    onClick={() => setShowPricingPopup(true)}
                    className="p-1.5 text-white bg-purple-600 rounded-md hover:bg-purple-700"
                    title="Pricing Calculator"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z" />
                    </svg>
                  </button>
                </div>
              </div>
              <button
                onClick={onClose}
                className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
              >
                Exit
              </button>
            </header>

            <main className="flex-1 overflow-y-auto p-4 bg-gray-50">
              {messages.length > 0 ? (
                messages.map((msg, index) => (
                  <div
                    key={index}
                    className={`mb-4 p-3 rounded-lg shadow-sm max-w-[80%] ${msg.direction === 'outgoing' ? 'bg-blue-600 text-white ml-auto' : 'bg-white text-gray-900 border'}`}
                  >
                    {/* Text message */}
                    {msg.message && (
                      <p className="text-sm whitespace-pre-wrap">{msg.message}</p>
                    )}

                    {/* Media attachments */}
                    {msg.media && msg.media.length > 0 && (
                      <div className={`${msg.message ? 'mt-2' : ''}`}>
                        {/* Images */}
                        {(() => {
                          const images = msg.media.filter(item => item.contentType && item.contentType.startsWith('image/'));
                          const nonImages = msg.media.filter(item => !item.contentType || !item.contentType.startsWith('image/'));

                          return (
                            <>
                              {/* Image Grid */}
                              {images.length > 0 && (
                                <div className={`grid gap-2 ${images.length === 1 ? 'grid-cols-1' : images.length === 2 ? 'grid-cols-2' : 'grid-cols-2'} mb-2`}>
                                  {images.map((mediaItem, mediaIndex) => (
                                    <div key={mediaIndex} className="relative group">
                                      <img
                                        src={mediaItem.url}
                                        alt={`Image ${mediaIndex + 1}`}
                                        className="w-full h-auto rounded-lg cursor-pointer hover:opacity-90 transition-opacity object-cover"
                                        style={{
                                          maxHeight: images.length === 1 ? '300px' : '150px',
                                          minHeight: images.length === 1 ? 'auto' : '100px'
                                        }}
                                        onClick={() => setSelectedImage(mediaItem)}
                                        onError={(e) => {
                                          console.error('Error loading image:', mediaItem.url);
                                          e.target.style.display = 'none';
                                          e.target.nextSibling.style.display = 'block';
                                        }}
                                      />
                                      {/* Error fallback */}
                                      <div
                                        className="hidden bg-gray-200 p-4 rounded-lg text-center text-gray-600 h-24 flex items-center justify-center"
                                        style={{ display: 'none' }}
                                      >
                                        <div>
                                          <p className="text-sm">📷 Failed to load</p>
                                          <a
                                            href={mediaItem.url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-blue-500 hover:text-blue-700 text-xs underline"
                                          >
                                            View original
                                          </a>
                                        </div>
                                      </div>
                                      {/* Hover overlay */}
                                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity rounded-lg flex items-center justify-center">
                                        <span className="text-white opacity-0 group-hover:opacity-100 transition-opacity text-sm">
                                          🔍 Click to view
                                        </span>
                                      </div>
                                      {/* Image count indicator for multiple images */}
                                      {images.length > 1 && (
                                        <div className="absolute top-1 right-1 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                                          {mediaIndex + 1}/{images.length}
                                        </div>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              )}

                              {/* Non-image attachments */}
                              {nonImages.map((mediaItem, mediaIndex) => (
                                <div key={`non-image-${mediaIndex}`} className="bg-gray-100 p-3 rounded-lg border mb-2">
                                  <div className="flex items-center space-x-2">
                                    <span className="text-lg">📎</span>
                                    <div>
                                      <p className="text-sm font-medium text-gray-700">
                                        {mediaItem.filename || 'Attachment'}
                                      </p>
                                      <p className="text-xs text-gray-500">
                                        {mediaItem.contentType || 'Unknown type'}
                                      </p>
                                    </div>
                                  </div>
                                  <a
                                    href={mediaItem.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="inline-block mt-2 text-blue-500 hover:text-blue-700 text-sm underline"
                                  >
                                    Open attachment
                                  </a>
                                </div>
                              ))}
                            </>
                          );
                        })()}
                      </div>
                    )}

                    <div className="flex items-center justify-between mt-1">
                      <span className="text-xs text-gray-400">{msg.timestamp}</span>
                      {msg.media && msg.media.length > 0 && (
                        <span className="text-xs text-gray-400 flex items-center">
                          📎 {msg.media.length}
                        </span>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center">No messages yet.</p>
              )}
              <div ref={messagesEndRef} />
            </main>

            <footer className="sticky bottom-0 bg-white border-t p-4 space-y-0">              {/* Shortcut select remains the same */}
              <select
                onChange={(e) => applyShortcut(e.target.value)}
                className="w-full p-2 border rounded-md text-gray-700 focus:ring-2 focus:ring-blue-500"
                aria-label="Select shortcut message"
              >
                <option value="">Shortcut Messages</option>
                <option value="getAvailability">Get Availability</option>
                <option value="onMyWay">On My Way</option>
                <option value="arrived">Arrived</option>
                <option value="delay">Delay</option>
                <option value="early">Early Finish</option>
                <option value="finished1">Close to Wrapping Up</option>
                <option value="finished2">Finished Detail</option>
                <option value="LWRReview">Leave a Review</option>
              </select>

              {/* Textarea moved out, now takes full width on its own row */}
              <textarea
                ref={textareaRef}
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder="Type a message..."
                className="w-full p-2 border rounded-md resize-none min-h-[2.5rem] focus:ring-2 focus:ring-blue-500"
                style={{ overflowY: 'hidden' }}
                onInput={(e) => {
                  e.target.style.height = 'auto';
                  const newH = Math.min(e.target.scrollHeight, MAX_HEIGHT);
                  e.target.style.height = `${newH}px`;
                  e.target.style.overflowY = e.target.scrollHeight > MAX_HEIGHT ? 'auto' : 'hidden';
                }}
                aria-label="Message input"
              />

              {/* New container for the bottom row controls, aligned to the right */}
              <div className="flex flex-row justify-end gap-2">
                {/* Delay Select */}
                <select
                  onChange={(e) => setDelay(Number(e.target.value))}
                  value={delay}
                  // Removed flex-1
                  className="p-2 border rounded-md focus:ring-2 focus:ring-blue-500"
                  aria-label="Select send delay"
                >
                  <option value={0}>⌛</option>
                  <option value={1}>1 Minute</option>
                  <option value={5}>5 Minutes</option>
                  <option value={10}>10 Minutes</option>
                </select>

                {/* Auto Button */}
                <button
                  onClick={generateAutoResponse}
                  className="px-4 py-2 rounded-md text-white bg-orange-500 hover:bg-orange-600 transition-colors"
                  aria-label="Auto"
                  // Allow clicking even when loading, but show disabled state
                  disabled={isGeneratingResponse}
                >
                  {isGeneratingResponse ? 'Generating...' : 'Auto🪄'}
                </button>

                {/* Send Button */}
                <button
                  onClick={handleSendMessage}
                  disabled={isLoading || isGeneratingResponse} // Also disable send while generating
                  // Removed flex-1
                  className={`px-4 py-2 rounded-md text-white ${(isLoading || isGeneratingResponse) ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 transition-colors'}`}
                  aria-label="Send message"
                >
                  {/* Use isLoading for Sending state, as generating is handled by Auto button text */}
                  {isLoading ? 'Sending...' : 'Send'}
                </button>
              </div>
            </footer>
          </>
        )}
      </div>

      {/* Pricing Popup */}
      {/* Image Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative max-w-4xl max-h-full p-4">
            <img
              src={selectedImage.url}
              alt={selectedImage.filename || 'Image'}
              className="max-w-full max-h-full object-contain rounded-lg"
              onClick={(e) => e.stopPropagation()}
            />
            <button
              onClick={() => setSelectedImage(null)}
              className="absolute top-2 right-2 bg-black bg-opacity-50 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-75 transition-opacity"
            >
              ✕
            </button>
            <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-3 py-1 rounded">
              {selectedImage.filename || 'Image'}
            </div>
            <a
              href={selectedImage.url}
              target="_blank"
              rel="noopener noreferrer"
              className="absolute bottom-2 right-2 bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm"
              onClick={(e) => e.stopPropagation()}
            >
              Open Original
            </a>
          </div>
        </div>
      )}

      <PricingPopup
        isOpen={showPricingPopup}
        onClose={() => setShowPricingPopup(false)}
      />
    </div>
  );
}

MessengerPopup.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  businessNumber: PropTypes.string.isRequired,
  clientNumber: PropTypes.string.isRequired,
  location: PropTypes.string,
};

MessengerPopup.defaultProps = {
  location: '',
};