"use client";
export const dynamic = "force-dynamic";

import { useAuthState } from "react-firebase-hooks/auth";
import { useCollection, useDocument } from "react-firebase-hooks/firestore";
import { collection, query, where, addDoc, updateDoc, deleteDoc, doc } from "firebase/firestore";
import { auth, db } from "../../lib/firebase/firebase";
import { useState, useEffect, useRef } from "react";
import { startOfWeek, eachDayOfInterval, addDays, isSameDay, format, differenceInMinutes } from "date-fns";
import { Timestamp } from "firebase/firestore";
import { Trash2, Pencil } from "lucide-react";

export default function TimePage() {
    const [user, loading, error] = useAuthState(auth);
    const [wageDoc, wageLoading, wageError] = useDocument(user ? doc(db, "users", user.uid) : null);

    useEffect(() => {
        console.log("TimePage - Component mounted on client");
    }, []);

    if (loading)
        return <div className="text-center text-gray-500 py-20">Loading authentication...</div>;
    if (error)
        return (
            <div className="text-center text-red-500 py-20">
                Auth Error: {error.message}
            </div>
        );
    if (!user)
        return <div className="text-center text-gray-500 py-20">Please sign in to view this page.</div>;

    const wage = wageDoc && wageDoc.exists() ? wageDoc.data().wage || 0 : 0;

    // In TimePage component's return statement
    return <TimeTrackerContent user={user} wageDoc={wageDoc} />;
}

function TimeTrackerContent({ user, wageDoc }) {
    const branch = wageDoc?.data()?.branch;
    const q = branch
        ? query(
            collection(db, "employeeTime"),
            where("userId", "==", user.uid),
            where("branch", "==", branch)
        )
        : null;


    const [snapshot, snapshotLoading, snapshotError] = useCollection(q);
    const [date, setDate] = useState(new Date().toISOString().split("T")[0]);
    const [startTime, setStartTime] = useState("08:00");
    const [endTime, setEndTime] = useState("15:00");
    const [tookBreak, setTookBreak] = useState(false);
    const [breakMinutes, setBreakMinutes] = useState(0);
    const [editingId, setEditingId] = useState(null);
    const formRef = useRef(null);
    const [paymentType, setPaymentType] = useState('hourly');
    const [commissionRate, setCommissionRate] = useState(0.27);
    const [jobRevenue, setJobRevenue] = useState(0);
    const [description, setDescription] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handlePaymentTypeChange = (type) => {
        setPaymentType(type);
        // Reset relevant fields when switching
        if (type === 'hourly') {
            setJobRevenue(0);
        } else {
            setStartTime("08:00");
            setEndTime("15:00");
            setTookBreak(false);
            setBreakMinutes(0);
        }
    };

    // Get wage from wageDoc
    const wage = wageDoc?.data()?.wage || 0;

    // Update payment type and commission rate when wageDoc changes
    useEffect(() => {
        if (wageDoc?.exists()) {
            setPaymentType(wageDoc.data().paymentType || 'hourly');
            setCommissionRate(wageDoc.data().commissionRate || 0.27);
        }
    }, [wageDoc]);

    // Handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();
        if (isSubmitting) return;
        setIsSubmitting(true);

        // Use state values for payment type and commission
        if (paymentType === 'hourly') {
            const hours = parseFloat(calculateHours());
            if (!hours || hours <= 0) {
                alert("Invalid time duration");
                return;
            }
        } else {
            const revenue = parseFloat(jobRevenue);
            if (!revenue || revenue <= 0) {
                alert("Invalid job revenue");
                return;
            }
        }

        const entryDate = new Date(`${date}T00:00:00`);
        const entryData = {
            userId: user.uid,
            date: Timestamp.fromDate(entryDate),
            paymentType,
            startTime,
            endTime,
            branch: branch,
            tookBreak,
            description,
            breakMinutes: tookBreak ? breakMinutes : 0,
            ...(paymentType === 'hourly' ? {
                hours: calculateHours(),
                wage: wage,
            } : {
                jobRevenue: parseFloat(jobRevenue),
                commissionRate: commissionRate,
                hours: 0 // Explicitly set hours to 0 for commission entries
            })
        };

        try {
            if (editingId) {
                await updateDoc(doc(db, "employeeTime", editingId), entryData);
                setEditingId(null);
            } else {
                await addDoc(collection(db, "employeeTime"), entryData);
            }
            resetForm();
        } catch (error) {
            console.error("Error saving time entry:", error);
            alert("Failed to save time entry");
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleEdit = (entry) => {
        setPaymentType(entry.paymentType);

        setEditingId(entry.id);
        setDate(format(entry.date.toDate(), "yyyy-MM-dd"));
        setStartTime(entry.startTime);
        setEndTime(entry.endTime);
        setTookBreak(entry.tookBreak ?? false);
        setBreakMinutes(entry.breakMinutes ?? 0);
        setDescription(entry.description || '');
        if (entry.paymentType === 'commission') {
            setJobRevenue(entry.jobRevenue);
        } else {
            // Reset commission-related fields
            setJobRevenue(0);
        }


        formRef.current?.scrollIntoView({ behavior: "smooth", block: "start" });
    };

    const handleDelete = async (id) => {
        if (confirm("Are you sure you want to delete this entry?")) {
            try {
                await deleteDoc(doc(db, "employeeTime", id));
            } catch (error) {
                console.error("Error deleting time entry:", error);
                alert("Failed to delete time entry");
            }
        }
    };

    const resetForm = () => {
        setEditingId(null);
        setDate(new Date().toISOString().split("T")[0]);
        setStartTime("08:00");
        setEndTime("15:00");
        setTookBreak(false);
        setBreakMinutes(0);
    };
    const calculateHours = () => {
        if (paymentType === 'commission') return 0.00; // Commission doesn't use hours

        const [startHours, startMinutes] = startTime.split(':').map(Number);
        const [endHours, endMinutes] = endTime.split(':').map(Number);

        const totalStartMinutes = startHours * 60 + startMinutes;
        const totalEndMinutes = endHours * 60 + endMinutes;
        let diffMinutes = totalEndMinutes - totalStartMinutes;

        if (diffMinutes < 0) {
            diffMinutes += 24 * 60; // Handle overnight shifts
        }

        const totalMinutes = tookBreak ? diffMinutes - breakMinutes : diffMinutes;
        const hours = totalMinutes / 60;

        return hours >= 0 ? hours.toFixed(2) : 0.00;
    };
    const weeks = snapshot?.docs?.reduce((acc, docSnapshot) => {
        const entry = { id: docSnapshot.id, ...docSnapshot.data() };
        const entryDate = entry.date.toDate();
        const weekStart = startOfWeek(entryDate, { weekStartsOn: 0 }); // Sunday start
        const weekKey = weekStart.toISOString();

        if (!acc[weekKey]) {
            acc[weekKey] = [];
        }
        acc[weekKey].push(entry);
        return acc;
    }, {});

    // Add sorting logic for week keys
    const sortedWeekKeys = weeks ? Object.keys(weeks).sort((a, b) =>
        new Date(b) - new Date(a) // Sort descending (newest first)
    ) : [];


    return (
        <div className="w-full max-w-4xl mx-auto p-6 bg-white rounded-xl shadow-lg">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-3xl font-bold text-gray-800">Time Tracker</h1>
                <p className="text-lg text-gray-600">
                    {paymentType === 'commission' ? (
                        `Commission Rate: ${(commissionRate * 100).toFixed(0)}%`
                    ) : (
                        `Current Wage: $${wage.toFixed(2)}/hr`
                    )}
                </p>
                <p className="text-sm text-gray-500">
                    Branch: {branch?.toUpperCase() || 'Not assigned'}
                </p>            </div>

            <form
                ref={formRef}
                onSubmit={handleSubmit}
                className="mb-8 bg-gray-50 p-6 rounded-lg shadow-inner"
            >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Date Field - Always Visible */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                        <input
                            type="date"
                            value={date}
                            onChange={(e) => setDate(e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                            required
                        />
                    </div>

                    {/* Payment Type Dropdown - Now Enabled */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Payment Type</label>
                        <select
                            value={paymentType}
                            onChange={(e) => handlePaymentTypeChange(e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="hourly">Hourly</option>
                            <option value="commission">Commission</option>
                        </select>
                    </div>

                    {/* Conditional Fields */}
                    {paymentType === 'hourly' ? (
                        <>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Start Time</label>
                                <input
                                    type="time"
                                    value={startTime}
                                    onChange={(e) => setStartTime(e.target.value)}
                                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                                    required
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">End Time</label>
                                <input
                                    type="time"
                                    value={endTime}
                                    onChange={(e) => setEndTime(e.target.value)}
                                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                                    required
                                />
                            </div>
                        </>
                    ) : (
                        <div className="md:col-span-2">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Job Revenue ($)
                            </label>
                            <input
                                type="number"
                                step="0.01"
                                value={jobRevenue}
                                onChange={(e) => setJobRevenue(e.target.value)}
                                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                                required
                            />
                        </div>
                    )}

                    {/* Description Field - Always Visible */}
                    <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Description
                        </label>
                        <input
                            type="text"
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                            placeholder="Work performed details"
                            required
                        />
                    </div>

                    {/* Break Fields - Only for Hourly */}
                    {paymentType === 'hourly' && (
                        <div className="flex items-center gap-4">
                            <label className="block text-sm font-medium text-gray-700">Took a Break?</label>
                            <input
                                type="checkbox"
                                checked={tookBreak}
                                onChange={(e) => setTookBreak(e.target.checked)}
                                className="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            {tookBreak && (
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Break (minutes)
                                    </label>
                                    <input
                                        type="number"
                                        min="0"
                                        value={breakMinutes}
                                        onChange={(e) => setBreakMinutes(parseInt(e.target.value) || 0)}
                                        className="w-20 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                                    />
                                </div>
                            )}
                        </div>
                    )}
                </div>

                <div className="mt-6 flex justify-between items-center">
                    {paymentType === 'hourly' ? (
                        <p className="text-gray-600">
                            Total Hours: <span className="font-semibold">{calculateHours()}</span>
                        </p>
                    ) : (
                        <p className="text-gray-600">
                            Commission Amount: <span className="font-semibold">
                                ${(jobRevenue * commissionRate).toFixed(2)}
                            </span>
                        </p>
                    )}
                    <div className="flex gap-4">
                        {editingId && (
                            <button
                                type="button"
                                onClick={resetForm}
                                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition"
                            >
                                Cancel
                            </button>
                        )}
                        <button
                            type="submit"
                            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition"
                        >
                            {editingId ? "Update" : "Submit"}
                        </button>
                    </div>
                </div>
            </form>

            <h2 className="text-2xl font-semibold text-gray-800 mb-4">Weekly Summaries</h2>
            {sortedWeekKeys.length === 0 ? (
                <p className="text-red-500 text-center">
                    No time entries yet. Start by submitting your hours above!
                </p>
            ) : (
                sortedWeekKeys.map((weekKey) => (
                    <WeeklySummary
                        key={weekKey}
                        weekStart={new Date(weekKey)}
                        entries={weeks[weekKey]}
                        onEdit={handleEdit}
                        onDelete={handleDelete}
                    />
                ))
            )}
        </div>
    );
}

function WeeklySummary({ weekStart, entries, onEdit, onDelete }) {
    const days = eachDayOfInterval({ start: weekStart, end: addDays(weekStart, 6) });
    const dailyTotals = days.map((day) => {
        const dayEntries = entries.filter((entry) => isSameDay(entry.date.toDate(), day));
        const totalHours = dayEntries.reduce((sum, entry) => sum + parseFloat(entry.hours || "0"), 0);
        const dailyWage = dayEntries.reduce(
            (sum, entry) => sum + parseFloat(entry.hours || "0") * entry.wage,
            0
        );
        return { day, totalHours, dailyWage, entries: dayEntries };
    });
    const weeklyTotalHours = dailyTotals.reduce((sum, { totalHours }) => sum + totalHours, 0);
    const weeklyTotalWage = dailyTotals.reduce((sum, { dailyWage }) => sum + dailyWage, 0);
    const overtimeHours = Math.max(weeklyTotalHours - 40, 0);
    const overtimeWage = overtimeHours * (entries[0]?.wage || 0) * 1.5;

    return (
        <div className="mb-8 bg-gray-50 p-4 rounded-lg shadow-md">
            <h3 className="text-lg font-medium text-gray-800 mb-2">
                Week of {format(weekStart, "MMMM d, yyyy")}
            </h3>
            <table className="w-full border-collapse">
                <thead>
                    <tr className="bg-gray-200">
                        <th className="border p-2 text-left text-gray-700">Date</th>
                        <th className="border p-2 text-left text-gray-700">Description</th>
                        <th className="border p-2 text-left text-gray-700">Start</th>
                        <th className="border p-2 text-left text-gray-700">End</th>
                        <th className="border p-2 text-left text-gray-700">Type</th>
                        <th className="border p-2 text-left text-gray-700">Hours</th>
                        <th className="border p-2 text-left text-gray-700">Revenue</th>
                        <th className="border p-2 text-left text-gray-700">Commission</th>
                        <th className="border p-2 text-right text-gray-700">Amount</th>
                        <th className="border p-2 text-right text-gray-700">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {dailyTotals.map(({ day, totalHours, dailyWage, entries }) =>
                        entries.map((entry) => (
                            <tr key={entry.id} className="hover:bg-gray-100">
                                <td className="border p-2">{format(day, "MM/dd")}</td>
                                <td className="border p-2 max-w-[200px] truncate">
                                    {entry.description || 'No description'}
                                </td>
                                <td className="border p-2">{entry.startTime}</td>
                                <td className="border p-2">{entry.endTime}</td>
                                <td className="border p-2 capitalize">
                                    {entry.paymentType}
                                </td>
                                <td className="border p-2 text-right">
                                    {entry.paymentType === 'hourly' ?
                                        `${entry.hours} hrs` : '—'
                                    }
                                </td>
                                <td className="border p-2 text-right">
                                    {entry.paymentType === 'commission' ?
                                        `$${entry.jobRevenue?.toFixed?.(2) ?? '0.00'}` : '—'
                                    }
                                </td>
                                <td className="border p-2 text-right">
                                    {entry.paymentType === 'commission' ?
                                        `${(entry.commissionRate * 100).toFixed(0)}%` : '—'
                                    }
                                </td>
                                <td className="border p-2 text-right">
                                    {entry.paymentType === 'commission' ?
                                        `$${(entry.jobRevenue * entry.commissionRate).toFixed(2)}` :
                                        `$${(entry.hours * entry.wage).toFixed(2)}`
                                    }
                                </td>
                                <td className="border p-2 text-right">
                                    <button
                                        onClick={() => onEdit(entry)}
                                        className="text-blue-600 hover:text-blue-800 mr-2"
                                    >
                                        <Pencil className="h-4 w-4" />
                                    </button>
                                    <button
                                        onClick={() => onDelete(entry.id)}
                                        className="text-red-600 hover:text-red-800"
                                    >
                                        <Trash2 className="h-4 w-4" />
                                    </button>
                                </td>
                            </tr>
                        ))
                    )}
                    <tr className="font-semibold bg-gray-100">
                        <td className="border p-2" colSpan="5">
                            Total Hours
                        </td>
                        <td className="border p-2 text-right">{weeklyTotalHours.toFixed(1)}</td>
                        <td className="border p-2 text-right">${weeklyTotalWage.toFixed(2)}</td>
                        <td className="border p-2"></td>
                    </tr>
                    <tr className="font-semibold bg-gray-100">
                        <td className="border p-2" colSpan="5">
                            Overtime
                        </td>
                        <td className="border p-2 text-right">{overtimeHours.toFixed(1)}</td>
                        <td className="border p-2 text-right">${overtimeWage.toFixed(2)}</td>
                        <td className="border p-2"></td>
                    </tr>
                </tbody>
            </table>
        </div>
    );
}
