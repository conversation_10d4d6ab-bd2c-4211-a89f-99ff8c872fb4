'use client';
import React, { useState } from 'react';
import TrainingSlideshow from '../../components/trainingSlide';

export default function TrainingPage() {
    const [activeSection, setActiveSection] = useState('before');
    const [searchQuery, setSearchQuery] = useState('');
    const [viewType, setViewType] = useState('exterior');

    const sections = [
        { id: 'before', title: 'Before the Detail' },
        { id: 'detailing-process', title: 'Detailing Process' },
        { id: 'inspection', title: 'Final Inspection & Quality Check' },
        { id: 'payment', title: 'Payment & Customer Satisfaction' },
        { id: 'reference', title: 'Quick Reference Guide' }
    ];

    const renderBeforeSection = () => (
        <div className="space-y-6">
            <h2 className="text-2xl font-bold border-b-2 border-black pb-2">Before the Detail</h2>
            <div className="grid md:grid-cols-2 gap-6">
                <div className="border border-gray-300 p-4">
                    <h3 className="text-lg font-bold mb-3">Pre-Service Checklist</h3>
                    <ul className="space-y-2 text-sm">
                        <li>□ Ensure 10+ gallons in water tank</li>
                        <li>□ Top off gas in van and generator</li>
                        <li>□ Fill 5-gallon tank</li>
                        <li>□ Check clean towel supply</li>
                        <li>□ Stock and organize all equipment</li>
                        <li>□ Review customer service details</li>
                        <li>□ Plan route and check traffic</li>
                    </ul>
                    <h4 className="font-bold mt-4 mb-2">Customer Communication</h4>
                    <ul className="space-y-1 text-sm">
                        <li>□ Notify customer we're en route</li>
                        <li>□ Provide accurate ETA</li>
                        <li>□ Drive safely</li>
                        <li>□ Notify upon arrival</li>
                    </ul>
                </div>
                <div className="border border-gray-300 p-4">
                    <h3 className="text-lg font-bold mb-3">Customer Greeting & Setup</h3>
                    <ol className="space-y-2 text-sm">
                        <li>1. Greet customer professionally</li>
                        <li>2. Collect vehicle keys</li>
                        <li>3. Ask about specific concerns or priorities</li>
                        <li>4. Confirm services to be performed</li>
                        <li>5. Set customer expectations</li>
                        <li>6. Begin equipment setup</li>
                    </ol>
                    <h4 className="font-bold mt-4 mb-2">Payment Policy Reminders</h4>
                    <div className="bg-gray-100 p-3 text-sm">
                        <p><strong>Standard:</strong> Payment after completion for new customers</p>
                        <p><strong>Exception:</strong> If customer won't be present, arrange payment before service</p>
                    </div>
                    <h4 className="font-bold mt-3 mb-2">Never Guarantee:</h4>
                    <ul className="space-y-1 text-sm text-red-700">
                        <li>• Perfect results or complete restoration</li>
                        <li>• 100% stain or pet hair removal (aim for 75%)</li>
                        <li>• Specific completion times</li>
                    </ul>
                </div>
            </div>
        </div>
    );

    const renderInspectionSection = () => (
        <div className="space-y-6">
            <h2 className="text-2xl font-bold border-b-2 border-black pb-2">Final Inspection & Quality Check</h2>
            <div className="grid md:grid-cols-2 gap-6">
                <div className="border border-gray-300 p-4">
                    <h3 className="text-lg font-bold mb-3">Critical Areas to Inspect</h3>
                    <div className="space-y-4">
                        <div>
                            <h4 className="font-bold">Driver's Area</h4>
                            <ul className="text-sm space-y-1">
                                <li>□ Driver's seat clean and conditioned</li>
                                <li>□ Dashboard dust-free</li>
                                <li>□ Steering wheel clean</li>
                                <li>□ No residue on surfaces</li>
                            </ul>
                        </div>
                        <div>
                            <h4 className="font-bold">Headliner & Visors</h4>
                            <ul className="text-sm space-y-1">
                                <li>□ Headliner free of dust/marks</li>
                                <li>□ Sun visors clean</li>
                                <li>□ No water spots or stains</li>
                            </ul>
                        </div>
                        <div>
                            <h4 className="font-bold">Hard-to-Reach Areas</h4>
                            <ul className="text-sm space-y-1">
                                <li>□ Crevices between seat and console</li>
                                <li>□ Door panel edges</li>
                                <li>□ Air vents clean</li>
                                <li>□ Use small brushes/compressed air</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div className="border border-gray-300 p-4">
                    <h3 className="text-lg font-bold mb-3">Final Quality Checklist</h3>
                    <div className="space-y-4">
                        <div>
                            <h4 className="font-bold">Center Console & Details</h4>
                            <ul className="text-sm space-y-1">
                                <li>□ Cup holders debris-free</li>
                                <li>□ No sticky residues</li>
                                <li>□ All buttons/controls clean</li>
                                <li>□ Storage compartments wiped</li>
                            </ul>
                        </div>
                        <div>
                            <h4 className="font-bold">Windows & Glass</h4>
                            <ul className="text-sm space-y-1">
                                <li>□ Interior windows streak-free</li>
                                <li>□ Exterior windows clean</li>
                                <li>□ Check from outside for streaks</li>
                                <li>□ Mirrors spotless</li>
                            </ul>
                        </div>
                        <div>
                            <h4 className="font-bold">Exterior Final Check</h4>
                            <ul className="text-sm space-y-1">
                                <li>□ No water spots</li>
                                <li>□ Tires properly dressed</li>
                                <li>□ Wheels and rims clean</li>
                                <li>□ Door jambs dry and clean</li>
                            </ul>
                        </div>
                    </div>
                    <div className="bg-green-100 p-3 mt-4">
                        <h4 className="font-bold text-sm">✅ Before Customer Inspection</h4>
                        <p className="text-sm">Complete 3-minute walkthrough, then contact customer to inspect results</p>
                    </div>
                </div>
            </div>
        </div>
    );

    const renderPaymentSection = () => (
        <div className="space-y-6">
            <h2 className="text-2xl font-bold border-b-2 border-black pb-2">Payment & Customer Satisfaction</h2>
            <div className="grid md:grid-cols-2 gap-6">
                <div className="border border-gray-300 p-4">
                    <h3 className="text-lg font-bold mb-3">Customer Satisfaction Process</h3>
                    <ol className="space-y-2 text-sm">
                        <li>1. Invite customer to inspect the completed work</li>
                        <li>2. Walk through the vehicle together</li>
                        <li>3. Address any concerns immediately</li>
                        <li>4. Ask for specific feedback on service quality</li>
                        <li>5. Confirm all services met expectations</li>
                        <li>6. Answer any questions about maintenance</li>
                    </ol>
                    <div className="bg-blue-100 p-3 mt-4">
                        <h4 className="font-bold text-sm">Customer Communication Tips</h4>
                        <ul className="text-sm space-y-1">
                            <li>• Be proactive about any limitations</li>
                            <li>• Explain what was accomplished</li>
                            <li>• Provide care recommendations</li>
                            <li>• Leave contact information</li>
                        </ul>
                    </div>
                </div>
                <div className="border border-gray-300 p-4">
                    <h3 className="text-lg font-bold mb-3">Payment Collection</h3>
                    <ol className="space-y-2 text-sm">
                        <li>1. Confirm agreed-upon service price</li>
                        <li>2. Verify payment method with customer</li>
                        <li>3. Process payment transaction</li>
                        <li>4. Provide receipt and service summary</li>
                        <li>5. Thank customer for their business</li>
                        <li>6. Offer business cards or referral materials</li>
                    </ol>
                    <div className="bg-yellow-100 p-3 mt-4">
                        <h4 className="font-bold text-sm">Communication During Service</h4>
                        <ul className="text-sm space-y-1">
                            <li>• <strong>If Delayed:</strong> Call ASAP with new ETA</li>
                            <li>• <strong>Price Changes:</strong> Get approval BEFORE proceeding</li>
                            <li>• <strong>Issues Found:</strong> Discuss solutions immediately</li>
                            <li>• <strong>Manager Updates:</strong> Regular check-ins required</li>
                        </ul>
                    </div>
                    <div className="bg-green-100 p-3 mt-4">
                        <h4 className="font-bold text-sm">Building Future Business</h4>
                        <ul className="text-sm space-y-1">
                            <li>• Ask about scheduling next service</li>
                            <li>• Explain membership benefits</li>
                            <li>• Request referrals if appropriate</li>
                            <li>• Leave positive lasting impression</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    );

    const renderReferenceSection = () => (
        <div className="space-y-6">
            <h2 className="text-2xl font-bold border-b-2 border-black pb-2">Quick Reference Guide</h2>
            <div className="grid md:grid-cols-3 gap-6">
                <div className="border border-gray-300 p-4">
                    <h3 className="text-lg font-bold mb-3">Emergency Contacts</h3>
                    <div className="space-y-2 text-sm">
                        <div>
                            <p className="font-bold">Manager/Supervisor:</p>
                            <p>Phone: _______________</p>
                        </div>
                        <div>
                            <p className="font-bold">Office/Dispatch:</p>
                            <p>Phone: _______________</p>
                        </div>
                        <div>
                            <p className="font-bold">Equipment Support:</p>
                            <p>Phone: _______________</p>
                        </div>
                        <div>
                            <p className="font-bold">Emergency Services:</p>
                            <p>911</p>
                        </div>
                    </div>
                </div>
                <div className="border border-gray-300 p-4">
                    <h3 className="text-lg font-bold mb-3">Common Troubleshooting</h3>
                    <div className="space-y-3 text-sm">
                        <div>
                            <p className="font-bold">Generator Won't Start:</p>
                            <ul className="space-y-1">
                                <li>• Check fuel level</li>
                                <li>• Verify oil level</li>
                                <li>• Check spark plug connection</li>
                                <li>• Contact support if persists</li>
                            </ul>
                        </div>
                        <div>
                            <p className="font-bold">Pressure Washer Issues:</p>
                            <ul className="space-y-1">
                                <li>• Check water supply</li>
                                <li>• Verify hose connections</li>
                                <li>• Clear any clogs in nozzle</li>
                                <li>• Check pump pressure settings</li>
                            </ul>
                        </div>
                        <div>
                            <p className="font-bold">Extraction Problems:</p>
                            <ul className="space-y-1">
                                <li>• Check vacuum hose connections</li>
                                <li>• Empty collection tank</li>
                                <li>• Verify suction power</li>
                                <li>• Check for blockages</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div className="border border-gray-300 p-4">
                    <h3 className="text-lg font-bold mb-3">Key Reminders & Don'ts</h3>
                    <div className="space-y-3">
                        <div className="bg-red-100 p-3">
                            <h4 className="font-bold text-sm text-red-800">❌ NEVER DO</h4>
                            <ul className="text-sm space-y-1">
                                <li>• Guarantee perfect results</li>
                                <li>• Use pressure washer on Level 1-2 interiors</li>
                                <li>• Leave equipment unattended</li>
                                <li>• Start work without customer approval</li>
                                <li>• Change prices mid-service without approval</li>
                            </ul>
                        </div>
                        <div className="bg-green-100 p-3">
                            <h4 className="font-bold text-sm text-green-800">✅ ALWAYS DO</h4>
                            <ul className="text-sm space-y-1">
                                <li>• Communicate delays immediately</li>
                                <li>• Check equipment before leaving</li>
                                <li>• Complete final 3-minute inspection</li>
                                <li>• Get customer sign-off</li>
                                <li>• Leave area clean</li>
                            </ul>
                        </div>
                        <div className="bg-blue-100 p-3">
                            <h4 className="font-bold text-sm text-blue-800">🔧 Equipment Care</h4>
                            <ul className="text-sm space-y-1">
                                <li>• Rinse all hoses after use</li>
                                <li>• Clean and organize tools</li>
                                <li>• Check fluid levels daily</li>
                                <li>• Report damage immediately</li>
                                <li>• Follow maintenance schedules</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div className="border border-gray-300 p-4 mt-6">
                <h3 className="text-lg font-bold mb-3">Chemical Safety & Ratios</h3>
                <div className="grid md:grid-cols-2 gap-4">
                    <div>
                        <h4 className="font-bold mb-2">Safety Requirements</h4>
                        <ul className="text-sm space-y-1">
                            <li>• Always wear protective equipment</li>
                            <li>• Ensure adequate ventilation</li>
                            <li>• Never mix different chemicals</li>
                            <li>• Keep MSDS sheets accessible</li>
                            <li>• Have eyewash/first aid available</li>
                        </ul>
                    </div>
                    <div>
                        <h4 className="font-bold mb-2">Mixing Instructions</h4>
                        <ul className="text-sm space-y-1">
                            <li>• Follow manufacturer ratios exactly</li>
                            <li>• Use measuring tools, not estimates</li>
                            <li>• Add chemical to water, not water to chemical</li>
                            <li>• Label all mixed solutions</li>
                            <li>• Dispose of waste properly</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    );

    const filteredSections = sections.filter(section =>
        section.title.toLowerCase().includes(searchQuery.toLowerCase())
    );

    return (
        <div className="min-h-screen bg-white text-black">
            {/* Header */}
            <div className="border-b-2 border-black bg-white sticky top-0 z-10">
                <div className="max-w-6xl mx-auto px-4 py-6">
                    <h1 className="text-3xl font-bold text-center">Detail On The Go</h1>
                    <p className="text-center text-lg mt-2">Professional Training Manual</p>
                    <div className="mt-4 max-w-md mx-auto">
                        <input
                            type="text"
                            placeholder="Search sections..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-black"
                        />
                    </div>
                </div>
            </div>

            <div className="max-w-6xl mx-auto px-4 py-8">
                {/* Navigation */}
                <div className="mb-8">
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                        {filteredSections.map((section) => (
                            <button
                                key={section.id}
                                onClick={() => setActiveSection(section.id)}
                                className={`px-4 py-3 border border-black transition-colors text-sm font-medium ${activeSection === section.id
                                        ? 'bg-black text-white'
                                        : 'bg-white text-black hover:bg-gray-100'
                                    }`}
                            >
                                {section.title}
                            </button>
                        ))}
                    </div>
                </div>

                {/* Content */}
                <div className="max-w-6xl mx-auto">
                    {activeSection === 'before' && renderBeforeSection()}
                    {activeSection === 'detailing-process' && (
                        <div className="space-y-6">
                            <h2 className="text-2xl font-bold border-b-2 border-black pb-2">
                                {viewType === 'exterior' ? 'Exterior Detailing Process' : 'Interior Detailing Process'}
                            </h2>
                            <div className="flex justify-center space-x-4 mb-6">
                                <button
                                    onClick={() => setViewType('exterior')}
                                    className={`px-4 py-2 border border-black ${viewType === 'exterior' ? 'bg-black text-white' : 'bg-white text-black'
                                        }`}
                                >
                                    Exterior
                                </button>
                                <button
                                    onClick={() => setViewType('interior')}
                                    className={`px-4 py-2 border border-black ${viewType === 'interior' ? 'bg-black text-white' : 'bg-white text-black'
                                        }`}
                                >
                                    Interior
                                </button>
                            </div>
                            <TrainingSlideshow type={viewType} />
                            {viewType === 'exterior' && (
                                <div className="grid md:grid-cols-2 gap-6">
                                    <div className="border border-gray-300 p-4">
                                        <h3 className="text-lg font-bold mb-3">Equipment Setup</h3>
                                        <ol className="space-y-2 text-sm">
                                            <li>1. Prepare cleaning supplies (bucket with soap, microfiber pad on wand)</li>
                                            <li>2. Start generator</li>
                                            <li>3. Prepare and start pressure washer</li>
                                            <li>4. Connect pressure washer gun BEFORE starting</li>
                                            <li>5. Extend pressure washer hose to vehicle</li>
                                        </ol>
                                        <h3 className="text-lg font-bold mb-3 mt-6">Initial Cleaning Steps</h3>
                                        <ol className="space-y-2 text-sm">
                                            <li>6. Clean wheels and rims first</li>
                                            <li>7. Use magic eraser on windows to remove bugs</li>
                                            <li>8. Angle windshield wipers up for full window access</li>
                                            <li>9. Pressure wash door jambs</li>
                                            <li>10. Pressure wash all exterior dirt</li>
                                        </ol>
                                    </div>
                                    <div className="border border-gray-300 p-4">
                                        <h3 className="text-lg font-bold mb-3">Washing & Drying Process</h3>
                                        <ol className="space-y-2 text-sm">
                                            <li>11. Apply foam to entire exterior</li>
                                            <li>12. Clean from TOP to BOTTOM</li>
                                            <li>13. Rinse wand in bucket multiple times per side</li>
                                            <li>14. Clean bottom/lower areas LAST</li>
                                            <li>15. Thoroughly rinse vehicle top to bottom</li>
                                            <li>16. Dry completely with ultra-soft car towel</li>
                                            <li>17. Start with sunny side before water dries</li>
                                            <li>18. Dry door jambs with microfiber cloths</li>
                                        </ol>
                                        <div className="bg-yellow-100 p-3 mt-4">
                                            <h4 className="font-bold text-sm">⚠️ Critical Reminders</h4>
                                            <ul className="text-sm space-y-1">
                                                <li>• Always work top to bottom</li>
                                                <li>• Rinse tools frequently</li>
                                                <li>• Don't let soap dry on vehicle</li>
                                                <li>• Check for streaks before moving on</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            )}
                            {viewType === 'interior' && (
                                <div className="grid md:grid-cols-2 gap-6">
                                    <div className="border border-gray-300 p-4">
                                        <h3 className="text-lg font-bold mb-3">Setup & Floor Mat Process</h3>
                                        <ol className="space-y-2 text-sm">
                                            <li>1. Extend electrical cord and start steamer</li>
                                            <li>2. Remove all floor mats</li>
                                            <li>3. Pressure wash floor mats</li>
                                            <li>4. Extract moisture from mats</li>
                                            <li>5. Place mats in sun to dry</li>
                                            <li>6. Connect vacuum and steamer hoses</li>
                                            <li>7. Start vehicle engine and blast AC</li>
                                        </ol>
                                        <h3 className="text-lg font-bold mb-3 mt-6">Interior Cleaning Sequence</h3>
                                        <ol className="space-y-2 text-sm">
                                            <li>8. Begin at driver's seat area</li>
                                            <li>9. Work systematically toward back</li>
                                            <li>10. Vacuum, wipe down, and steam each section</li>
                                            <li>11. Clean floor areas as you progress</li>
                                            <li>12. Replace mats only after floor is clean</li>
                                        </ol>
                                    </div>
                                    <div className="border border-gray-300 p-4">
                                        <h3 className="text-lg font-bold mb-3">Specialized Cleaning Techniques</h3>
                                        <div className="space-y-4">
                                            <div>
                                                <h4 className="font-bold">Pet Hair Removal</h4>
                                                <p className="text-sm">Use stone, rubber, or metal removers based on surface type. Aim for 75% removal.</p>
                                            </div>
                                            <div>
                                                <h4 className="font-bold">Shampoo & Extraction Process</h4>
                                                <ol className="text-sm space-y-1">
                                                    <li>1. Spray cleaning chemicals</li>
                                                    <li>2. Agitate with drill or hand brush</li>
                                                    <li>3. Extract moisture with extractor</li>
                                                    <li>4. Repeat up to 4 times if needed</li>
                                                    <li>5. Wipe with microfiber (doesn't need bone dry)</li>
                                                </ol>
                                            </div>
                                            <div>
                                                <h4 className="font-bold">Interior Pressure Washing</h4>
                                                <p className="text-sm bg-red-100 p-2">⚠️ Level 3+ interiors ONLY with client consent for extremely dirty conditions</p>
                                            </div>
                                        </div>
                                        <h3 className="text-lg font-bold mb-3 mt-6">Finishing Steps</h3>
                                        <ol className="space-y-2 text-sm">
                                            <li>13. Apply conditioner and requested products</li>
                                            <li>14. Clean all interior windows</li>
                                            <li>15. Dress tires and remove excess product</li>
                                        </ol>
                                    </div>
                                </div>
                            )}
                        </div>
                    )}
                    {activeSection === 'inspection' && renderInspectionSection()}
                    {activeSection === 'payment' && renderPaymentSection()}
                    {activeSection === 'reference' && renderReferenceSection()}
                </div>
            </div>
        </div>
    );
}