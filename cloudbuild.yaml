steps:
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'us-central1-docker.pkg.dev/$PROJECT_ID/dotg/dotg:latest', '.']

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'us-central1-docker.pkg.dev/$PROJECT_ID/dotg/dotg:latest']

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'dotg'
      - '--image=us-central1-docker.pkg.dev/$PROJECT_ID/dotg/dotg:latest'
      - '--platform=managed'
      - '--region=us-central1'
      - '--allow-unauthenticated'

options:
  machineType: 'E2_HIGHCPU_8'