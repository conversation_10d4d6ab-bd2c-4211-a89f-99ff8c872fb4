'use client';

import React, { useState, useEffect } from 'react';
import { collection, addDoc } from 'firebase/firestore';
import { onAuthStateChangedListener } from '../auth'; // Import from auth.js
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../lib/firebase/firebase';

const CreatePaymentLinkPopup = ({ onClose }) => {
    const [productName, setProductName] = useState('');
    const [description, setDescription] = useState('');
    const [amount, setAmount] = useState('');
    const [includeTax, setIncludeTax] = useState('Auto');
    const [collectTax, setCollectTax] = useState(true);
    const [collectAddress, setCollectAddress] = useState('none');
    const [requirePhone, setRequirePhone] = useState(false);
    const [limitPayments, setLimitPayments] = useState(false);
    const [paymentLimit, setPaymentLimit] = useState(1);
    const [isCreating, setIsCreating] = useState(false);
    const [paymentLink, setPaymentLink] = useState('');
    const [error, setError] = useState('');
    const [branch, setBranch] = useState('');
    const [isLoadingBranch, setIsLoadingBranch] = useState(true);
    const [copied, setCopied] = useState(false);

    // Fetch branch when component mounts
    useEffect(() => {
        const unsubscribe = onAuthStateChangedListener(async (user) => {
            if (user) {
                try {
                    const userRef = doc(db, 'users', user.uid);
                    const userDoc = await getDoc(userRef);
                    if (userDoc.exists()) {
                        const userData = userDoc.data();
                        setBranch(userData.branch || '');
                    } else {
                        setError('User profile not found. Please complete your profile.');
                    }
                } catch (err) {
                    setError('Failed to fetch user data: ' + err.message);
                    console.error(err);
                }
            } else {
                setError('You must be signed in to create a payment link.');
            }
            setIsLoadingBranch(false);
        });

        // Cleanup subscription on unmount
        return () => unsubscribe();
    }, []);

    const handleAmountChange = (e) => {
        const value = e.target.value;
        if (/^\d*\.?\d*$/.test(value)) {
            setAmount(value);
        }
    };

    const handleAmountBlur = () => {
        if (amount === '') return;
        const num = parseFloat(amount);
        if (isNaN(num)) {
            setAmount('');
        } else {
            setAmount(num.toFixed(2));
        }
    };

    const handleCreateLink = async () => {
        if (!productName || !amount || parseFloat(amount) <= 0) {
            setError('Product name and a valid amount are required');
            return;
        }
        if (!branch) {
            setError('Branch is required to create a payment link');
            return;
        }

        setIsCreating(true);
        setError('');

        try {
            const paymentLinkData = {
                productName,
                description,
                amount: parseFloat(amount),
                includeTax,
                collectTax,
                collectAddress,
                requirePhone,
                limitPayments,
                paymentLimit: limitPayments ? paymentLimit : null,
                isPaid: false,
                isOpened: false,
                createdAt: new Date().toISOString(),
                branch, // Always include branch
            };

            const docRef = await addDoc(collection(db, 'paymentLinks'), paymentLinkData);
            const customUrl = `https://detailongo.com/pay/${docRef.id}`;
            setPaymentLink(customUrl);
        } catch (err) {
            setError('Failed to create payment link: ' + err.message);
            console.error(err);
        } finally {
            setIsCreating(false);
        }
    };

    const handleCopyLink = () => {
        navigator.clipboard.writeText(paymentLink);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000); // Reset after 2 seconds
    };

    const handleCreateAnotherLink = () => {
        setPaymentLink('');
        setProductName('');
        setDescription('');
        setAmount('');
        setIncludeTax('Auto');
        setCollectTax(true);
        setCollectAddress('none');
        setRequirePhone(false);
        setLimitPayments(false);
        setPaymentLimit(1);
        setError('');
    };

    if (isLoadingBranch) {
        return (
            <div className="fixed inset-0 bg-gray-800 bg-opacity-50 flex justify-center items-center z-50">
                <div className="relative bg-white p-6 rounded-lg w-[90%] max-w-lg shadow-lg">
                    <p>Loading...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="fixed inset-0 bg-gray-800 bg-opacity-50 flex justify-center items-center z-50">
            <div className="relative bg-white p-6 rounded-lg w-[90%] max-w-lg shadow-lg max-h-[80vh] overflow-y-auto">
                <button
                    onClick={onClose}
                    disabled={isCreating}
                    className={`absolute top-3 right-3 rounded-lg px-4 py-2 bg-red-500 hover:bg-red-600 text-white ${isCreating ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                    Cancel
                </button>

                <h3 className="text-xl font-semibold mb-4">Create Payment Link</h3>
                {branch && <p className="text-gray-600 mb-4">Location: {branch}</p>}
                {error && <p className="text-red-500 mb-4">{error}</p>}

                {paymentLink ? (
                    <div className="space-y-4">
                        <div>
                            <label className="block text-gray-800 mb-1">Payment Link</label>
                            <div className="flex items-center gap-2">
                                <input
                                    type="text"
                                    value={paymentLink}
                                    readOnly
                                    className="w-full p-2 border rounded"
                                />
                                <button
                                    onClick={handleCopyLink}
                                    className="rounded-lg px-4 py-2 bg-green-500 hover:bg-green-600 text-white"
                                >
                                    {copied ? 'Copied!' : 'Copy'}
                                </button>
                            </div>
                        </div>
                        <button
                            onClick={handleCreateAnotherLink}
                            className="w-full rounded-lg px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white"
                        >
                            Create Another Link
                        </button>
                    </div>
                ) : (
                    <div className="space-y-4">
                        <div>
                            <label className="block text-gray-800 mb-1">Product Name</label>
                            <input
                                type="text"
                                value={productName}
                                onChange={(e) => setProductName(e.target.value)}
                                className="w-full p-2 border rounded"
                                placeholder="e.g., Car Wash"
                            />
                        </div>
                        <div>
                            <label className="block text-gray-800 mb-1">Description</label>
                            <textarea
                                value={description}
                                onChange={(e) => setDescription(e.target.value)}
                                className="w-full p-2 border rounded h-24"
                                placeholder="e.g., Premium car wash service"
                            />
                        </div>
                        <div>
                            <label className="block text-gray-800 mb-1">Amount ($)</label>
                            <input
                                type="text"
                                value={amount}
                                onChange={handleAmountChange}
                                onBlur={handleAmountBlur}
                                className="w-full p-2 border rounded"
                                placeholder="e.g., 10.00"
                            />
                        </div>
                        <div>
                            <label className="block text-gray-800 mb-1">Include Tax in Price</label>
                            <select
                                value={includeTax}
                                onChange={(e) => setIncludeTax(e.target.value)}
                                className="w-full p-2 border rounded"
                            >
                                <option value="Auto">Auto</option>
                                <option value="Yes">Yes</option>
                                <option value="No">No</option>
                            </select>
                        </div>
                        <div className="flex items-center gap-2">
                            <input
                                type="checkbox"
                                checked={collectTax}
                                onChange={(e) => setCollectTax(e.target.checked)}
                                className="h-4 w-4"
                            />
                            <label className="text-sm text-gray-700">Collect Tax Automatically</label>
                        </div>
                        <div>
                            <label className="block text-gray-800 mb-1">Collect Customer Addresses</label>
                            <select
                                value={collectAddress}
                                onChange={(e) => setCollectAddress(e.target.value)}
                                className="w-full p-2 border rounded"
                            >
                                <option value="none">None</option>
                                <option value="billing">Billing</option>
                                <option value="shipping">Shipping</option>
                                <option value="both">Both</option>
                            </select>
                        </div>
                        <div className="flex items-center gap-2">
                            <input
                                type="checkbox"
                                checked={requirePhone}
                                onChange={(e) => setRequirePhone(e.target.checked)}
                                className="h-4 w-4"
                            />
                            <label className="text-sm text-gray-700">Require Phone Number</label>
                        </div>
                        <div className="flex items-center gap-2">
                            <input
                                type="checkbox"
                                checked={limitPayments}
                                onChange={(e) => setLimitPayments(e.target.checked)}
                                className="h-4 w-4"
                            />
                            <label className="text-sm text-gray-700">Limit Number of Payments</label>
                            {limitPayments && (
                                <input
                                    type="number"
                                    value={paymentLimit}
                                    onChange={(e) => setPaymentLimit(Math.max(1, parseInt(e.target.value, 10)))}
                                    className="w-20 p-2 border rounded"
                                    min="1"
                                />
                            )}
                        </div>
                        <button
                            onClick={handleCreateLink}
                            disabled={isCreating || !branch}
                            className={`w-full rounded-lg px-4 py-2 text-white ${isCreating || !branch ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-500 hover:bg-blue-600'}`}
                        >
                            {isCreating ? 'Creating...' : 'Create Payment Link'}
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

export default CreatePaymentLinkPopup;