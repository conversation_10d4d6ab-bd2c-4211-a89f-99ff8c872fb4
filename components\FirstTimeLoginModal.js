import { useState } from 'react';

const FirstTimeLoginModal = ({ user, onClose, onSubmit }) => {
    const validBranches = ['lwr', 'dvr', 'w-stl', 'la'];
    const [address, setAddress] = useState('');
    const [selectedBranch, setSelectedBranch] = useState('');
    const [submitted, setSubmitted] = useState(false);

    const handleBranchClick = async (base) => {
        const normalized = validBranches.includes(base) ? base : 'lwr';
        const branchRequested = `${normalized}-requested`;
        // Pass form data as object to match header's expected signature
        await onSubmit({ address, branch: branchRequested });
        setSelectedBranch(branchRequested);
        setSubmitted(true);
    };

    return (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-50 flex justify-center items-center">
            <div className="bg-white p-6 rounded-lg shadow-lg w-96">
                {!submitted ? (
                    <>
                        <h2 className="text-2xl mb-4">First-time Login</h2>
                        <div className="mb-4">
                            <label htmlFor="address" className="block text-lg mb-2">
                                Address (optional):
                            </label>
                            <input
                                type="text"
                                id="address"
                                value={address}
                                onChange={(e) => setAddress(e.target.value)}
                                className="w-full px-4 py-2 border rounded-md"
                            />
                        </div>
                        <div className="mb-4">
                            <p className="text-lg mb-2">Select your branch:</p>
                            <div className="flex flex-wrap gap-2">
                                {validBranches.map((b) => (
                                    <button
                                        key={b}
                                        onClick={() => handleBranchClick(b)}
                                        className="px-4 py-2 bg-blue-500 text-white rounded-md"
                                    >
                                        {b.toUpperCase()}
                                    </button>
                                ))}
                            </div>
                        </div>
                        <button
                            onClick={onClose}
                            className="mt-4 w-full py-2 bg-gray-500 text-white rounded-lg"
                        >
                            Close
                        </button>
                    </>
                ) : (
                    <>
                        <h2 className="text-2xl mb-4">Request Received!</h2>
                        <p className="mb-4">
                            Your branch <strong>{selectedBranch}</strong> has been confirmed requested.
                            We'll be in touch shortly.
                        </p>
                        <button
                            onClick={onClose}
                            className="w-full py-2 bg-blue-500 text-white rounded-lg"
                        >
                            Close
                        </button>
                    </>
                )}
            </div>
        </div>
    );
};

export default FirstTimeLoginModal;
