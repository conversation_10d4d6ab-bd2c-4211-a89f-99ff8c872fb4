// components/NetworkStatus.js
'use client';

import { useEffect } from 'react';

export default function NetworkStatus() {
    useEffect(() => {
        const handleOffline = () => {
            alert('You are now offline. Some features may not work.');
        };

        window.addEventListener('offline', handleOffline);

        return () => {
            window.removeEventListener('offline', handleOffline);
        };
    }, []);

    return null;
}