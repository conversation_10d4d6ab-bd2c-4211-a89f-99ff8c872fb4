import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, X } from 'lucide-react';

const TrainingSlideshow = () => {
    const [currentSlide, setCurrentSlide] = useState(0);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalSlide, setModalSlide] = useState(0);

    const trainingImages = [
        {
            src: '/training/Exterior 1 Glass.png',
            title: 'Glass Cleaning',
            description: 'Proper technique for cleaning glass surfaces and removing bugs'
        },
        {
            src: '/training/Exterior 2 Wheels.png',
            title: 'Wheel Cleaning',
            description: 'Clean wheels and rims first before exterior washing'
        },
        {
            src: '/training/Exterior 3 Bugs.png',
            title: 'Bug Removal',
            description: 'Use magic eraser on windows to remove stubborn bugs'
        },
        {
            src: '/training/Exterior 4 Jambs.png',
            title: 'Door Jambs',
            description: 'Pressure wash door jambs for thorough cleaning'
        },
        {
            src: '/training/Exterior 5 Rinse.png',
            title: 'Initial Rinse',
            description: 'Pressure wash all exterior dirt before foam application'
        },
        {
            src: '/training/Exterior 6 Foam.png',
            title: 'Foam Application',
            description: 'Apply foam to entire exterior surface evenly'
        },
        {
            src: '/training/Exterior 7 Scrub.png',
            title: 'Scrubbing Technique',
            description: 'Clean from top to bottom, rinse wand frequently'
        },
        {
            src: '/training/Exterior 8 Dry.png',
            title: 'Drying Process',
            description: 'Use ultra-soft car towel, start with sunny side'
        },
        {
            src: '/training/Interior 1 Polish Door Jambs.png',
            title: 'Polish Door Jambs',
            description: 'Final polish and dry door jambs thoroughly'
        },
        {
            src: '/training/Interior 2 Remove Mats.png',
            title: 'Remove Floor Mats',
            description: 'Remove all floor mats and pressure wash separately'
        },
        {
            src: '/training/Interior 3 Drivers Door.png',
            title: 'Driver Door Panel',
            description: 'Clean driver door panel and controls thoroughly'
        },
        {
            src: '/training/Interior 4 Dash.png',
            title: 'Dashboard Cleaning',
            description: 'Dust-free dashboard with proper cleaning products'
        },
        {
            src: '/training/Interior 5 Center Console.png',
            title: 'Center Console',
            description: 'Clean cup holders and remove all debris and residues'
        },
        {
            src: '/training/Interior 6 Drivers Seat.png',
            title: 'Driver Seat',
            description: 'Clean and condition driver seat area first'
        },
        {
            src: '/training/Interior 7 Drivers Floor.png',
            title: 'Driver Floor Area',
            description: 'Vacuum and clean floor area systematically'
        }
    ];

    const nextSlide = () => {
        setCurrentSlide((prev) => (prev + 1) % trainingImages.length);
    };

    const prevSlide = () => {
        setCurrentSlide((prev) => (prev - 1 + trainingImages.length) % trainingImages.length);
    };

    const nextModalSlide = () => {
        setModalSlide((prev) => (prev + 1) % trainingImages.length);
    };

    const prevModalSlide = () => {
        setModalSlide((prev) => (prev - 1 + trainingImages.length) % trainingImages.length);
    };

    const openModal = (index) => {
        setModalSlide(index);
        setIsModalOpen(true);
    };

    const closeModal = () => {
        setIsModalOpen(false);
    };

    // Keyboard navigation
    useEffect(() => {
        const handleKeyPress = (e) => {
            if (isModalOpen) {
                if (e.key === 'ArrowLeft') prevModalSlide();
                if (e.key === 'ArrowRight') nextModalSlide();
                if (e.key === 'Escape') closeModal();
            } else {
                if (e.key === 'ArrowLeft') prevSlide();
                if (e.key === 'ArrowRight') nextSlide();
            }
        };

        window.addEventListener('keydown', handleKeyPress);
        return () => window.removeEventListener('keydown', handleKeyPress);
    }, [isModalOpen]);

    return (
        <div className="w-full max-w-4xl mx-auto bg-white">
            {/* Main Slideshow */}
            <div className="relative bg-gray-100 rounded-lg overflow-hidden shadow-lg">
                {/* Image Container */}
                <div className="relative h-96 overflow-hidden">
                    <div
                        className="flex transition-transform duration-500 ease-in-out"
                        style={{ transform: `translateX(-${currentSlide * 100}%)` }}
                    >
                        {trainingImages.map((image, index) => (
                            <div key={index} className="w-full flex-shrink-0 relative">
                                <img
                                    src={image.src}
                                    alt={image.title}
                                    className="w-full h-96 object-cover cursor-pointer hover:opacity-90 transition-opacity"
                                    onClick={() => openModal(index)}
                                />
                                {/* Image overlay with title */}
                                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                                    <h3 className="text-white text-xl font-bold">{image.title}</h3>
                                    <p className="text-white/90 text-sm mt-1">{image.description}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Navigation Arrows */}
                <button
                    onClick={prevSlide}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 hover:scale-110"
                >
                    <ChevronLeft size={24} />
                </button>

                <button
                    onClick={nextSlide}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 hover:scale-110"
                >
                    <ChevronRight size={24} />
                </button>

                {/* Slide Indicators */}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                    {trainingImages.map((_, index) => (
                        <button
                            key={index}
                            onClick={() => setCurrentSlide(index)}
                            className={`w-3 h-3 rounded-full transition-all duration-200 ${index === currentSlide
                                    ? 'bg-white scale-110'
                                    : 'bg-white/50 hover:bg-white/70'
                                }`}
                        />
                    ))}
                </div>
            </div>

            {/* Slide Counter */}
            <div className="text-center mt-4 text-gray-600">
                <span className="text-lg font-medium">
                    {currentSlide + 1} of {trainingImages.length}
                </span>
                <p className="text-sm mt-1">Click image to view full size</p>
            </div>

            {/* Thumbnail Navigation */}
            <div className="mt-6 grid grid-cols-5 md:grid-cols-8 gap-2">
                {trainingImages.map((image, index) => (
                    <button
                        key={index}
                        onClick={() => setCurrentSlide(index)}
                        className={`relative aspect-square rounded overflow-hidden transition-all duration-200 ${index === currentSlide
                                ? 'ring-2 ring-blue-500 scale-105'
                                : 'hover:scale-105 opacity-70 hover:opacity-100'
                            }`}
                    >
                        <img
                            src={image.src}
                            alt={image.title}
                            className="w-full h-full object-cover"
                        />
                    </button>
                ))}
            </div>

            {/* Modal for full-size images */}
            {isModalOpen && (
                <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4">
                    <div className="relative max-w-6xl max-h-full">
                        {/* Close button */}
                        <button
                            onClick={closeModal}
                            className="absolute top-4 right-4 z-10 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200"
                        >
                            <X size={24} />
                        </button>

                        {/* Modal navigation arrows */}
                        <button
                            onClick={prevModalSlide}
                            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-3 rounded-full transition-all duration-200 hover:scale-110 z-10"
                        >
                            <ChevronLeft size={32} />
                        </button>

                        <button
                            onClick={nextModalSlide}
                            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-3 rounded-full transition-all duration-200 hover:scale-110 z-10"
                        >
                            <ChevronRight size={32} />
                        </button>

                        {/* Modal image */}
                        <div className="relative">
                            <img
                                src={trainingImages[modalSlide].src}
                                alt={trainingImages[modalSlide].title}
                                className="max-w-full max-h-[90vh] object-contain rounded-lg"
                            />

                            {/* Modal image info */}
                            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6 rounded-b-lg">
                                <h3 className="text-white text-2xl font-bold">{trainingImages[modalSlide].title}</h3>
                                <p className="text-white/90 text-base mt-2">{trainingImages[modalSlide].description}</p>
                                <p className="text-white/70 text-sm mt-2">
                                    {modalSlide + 1} of {trainingImages.length} • Press ESC to close • Use arrow keys to navigate
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default TrainingSlideshow;