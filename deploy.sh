#!/bin/bash
set -e  # Exit immediately if any command fails

# Step 1: Build Next.js
echo "Building the DOTG app..."
npm run build

# Step 2: Build Docker image (point to Artifact Registry)
echo "Building Docker image..."
docker build -t us-central1-docker.pkg.dev/$(gcloud config get-value project)/dotg/dotg:latest .

# Step 3: Push Docker image
echo "Pushing Docker image to Artifact Registry..."
docker push us-central1-docker.pkg.dev/$(gcloud config get-value project)/dotg/dotg:latest

# Step 4: Deploy to Cloud Run
echo "Deploying to Google Cloud Run..."
gcloud run deploy dotg \
  --image us-central1-docker.pkg.dev/$(gcloud config get-value project)/dotg/dotg:latest \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars "NEXT_PUBLIC_UPLOADTHING_APP_ID=7dzxf2x6zw" \
  --set-env-vars "UPLOADTHING_SECRET=************************************************************************" \
  --set-env-vars "UPLOADTHING_TOKEN=************************************************************************************************************************************************************************"

echo "Deployment complete!"