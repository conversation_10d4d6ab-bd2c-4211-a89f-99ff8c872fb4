import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

const firebaseConfig = {
    apiKey: "AIzaSyBMVa4EhYrz2NyYBdaVMJTS-JjfUIQDagQ",
    authDomain: "detail-on-the-go-universal.firebaseapp.com",
    projectId: "detail-on-the-go-universal",
    storageBucket: "detail-on-the-go-universal.firebasestorage.app",
    messagingSenderId: "896343340170",
    appId: "1:896343340170:web:473d7fd278d40649de2973",
    measurementId: "G-W2D7QKW2YS"
};

const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);
