{"name": "my-next-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p ${PORT:-3000}", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@headlessui/react": "^2.2.0", "@shadcn/ui": "^0.0.4", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.5.0", "@tailwindcss/forms": "^0.5.10", "@uploadthing/react": "^7.1.5", "critters": "^0.0.25", "date-fns": "^4.1.0", "firebase": "^11.3.1", "google-auth-library": "^9.15.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.469.0", "next": "15.1.3", "openai": "^4.96.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-firebase-hooks": "^5.1.1", "react-hot-toast": "^2.5.1", "react-modal": "^3.16.3", "react-resizable": "^3.0.5", "react-table": "^7.8.0", "rrule": "^2.8.1", "stripe": "^17.5.0", "uploadthing": "^7.4.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/react": "19.0.12", "eslint": "^9", "eslint-config-next": "15.1.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "5.8.3"}}